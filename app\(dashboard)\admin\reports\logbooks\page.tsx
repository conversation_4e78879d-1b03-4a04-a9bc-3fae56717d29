'use client'

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { 
  Download, 
  Search, 
  Filter,
  BookOpen,
  Calendar,
  User,
  Clock,
  CheckCircle2,
  AlertCircle,
  Eye
} from "lucide-react"
import { Logbooks } from "@/features/logbook/server/Logbooks"
import { LogbookWithStudent, LogbookStatistics, LogbookFilters } from "@/features/logbook/types/logbook"
import { LogbookList } from "@/features/logbook/components/logbook-list"
import { toast } from "sonner"

export default function AdminLogbooksPage() {
  const [loading, setLoading] = useState(true)
  const [logbooks, setLogbooks] = useState<LogbookWithStudent[]>([])
  const [statistics, setStatistics] = useState<LogbookStatistics | null>(null)
  const [filters, setFilters] = useState<LogbookFilters>({
    status: 'all'
  })

  useEffect(() => {
    loadLogbooks()
    loadStatistics()
  }, [filters])

  const loadLogbooks = async () => {
    try {
      setLoading(true)
      const result = await Logbooks.getAllLogbooks(filters)
      setLogbooks(result?.data || [])
    } catch (error) {
      console.error("Failed to load logbooks:", error)
    } finally {
      setLoading(false)
    }
  }

  const loadStatistics = async () => {
    try {
      const result = await Logbooks.getLogbookStatistics()
      setStatistics(result?.data || null)
    } catch (error) {
      console.error("Failed to load statistics:", error)
    }
  }

  const handleDelete = async (id: string) => {
    try {
      await Logbooks.deleteLogbook(id)
      await loadLogbooks()
      await loadStatistics()
      toast.success("Logbook deleted successfully")
    } catch (error) {
      console.error("Failed to delete logbook:", error)
    }
  }

  const handleEdit = (logbook: LogbookWithStudent) => {
    // For admin, we might want to open a read-only view or redirect to student view
    toast.info("Admin editing functionality to be implemented")
  }

  const handleExport = async () => {
    try {
      // This would typically generate and download a CSV/Excel file
      toast.info("Export functionality to be implemented")
    } catch (error) {
      console.error("Failed to export logbooks:", error)
    }
  }

  const handleFilterChange = (key: keyof LogbookFilters, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: value === 'all' ? undefined : value
    }))
  }

  const getCompletionRate = () => {
    if (!statistics || statistics.total_logbooks === 0) return 0
    return Math.round((statistics.submitted_logbooks / statistics.total_logbooks) * 100)
  }

  return (
    <div className="flex flex-1 flex-col">
      <div className="@container/main flex flex-1 flex-col gap-2">
        <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
          <div className="flex flex-col gap-4 px-4 lg:px-6">
            <div className="flex items-center justify-between">
              <div className="flex flex-col gap-2">
                <h1 className="text-2xl font-bold">Logbook Management</h1>
                <p className="text-muted-foreground">
                  Monitor and manage student logbook entries
                </p>
              </div>
              <div className="flex items-center gap-2">
                <Button variant="outline" onClick={handleExport} className="flex items-center gap-2">
                  <Download className="h-4 w-4" />
                  Export Data
                </Button>
              </div>
            </div>

            {/* Statistics Overview */}
            {statistics && (
              <div className="grid gap-4 md:grid-cols-4">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Total Logbooks</CardTitle>
                    <BookOpen className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{statistics.total_logbooks}</div>
                    <p className="text-xs text-muted-foreground">
                      From {statistics.active_students} students
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Submitted</CardTitle>
                    <CheckCircle2 className="h-4 w-4 text-green-500" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-green-600">{statistics.submitted_logbooks}</div>
                    <p className="text-xs text-muted-foreground">
                      {getCompletionRate()}% completion rate
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Reviewed</CardTitle>
                    <Eye className="h-4 w-4 text-blue-500" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-blue-600">{statistics.reviewed_logbooks}</div>
                    <p className="text-xs text-muted-foreground">
                      Supervisor feedback provided
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Avg Hours</CardTitle>
                    <Clock className="h-4 w-4 text-purple-500" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-purple-600">
                      {statistics.average_hours ? statistics.average_hours.toFixed(1) : '0'}h
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Per logbook entry
                    </p>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Filters */}
            <Card>
              <CardContent className="p-4">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Search by student name or logbook title..."
                        value={filters.student_name || ''}
                        onChange={(e) => handleFilterChange('student_name', e.target.value)}
                        className="pl-10"
                      />
                    </div>
                  </div>

                  <Select
                    value={filters.status || 'all'}
                    onValueChange={(value) => handleFilterChange('status', value)}
                  >
                    <SelectTrigger className="w-full md:w-40">
                      <SelectValue placeholder="Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="draft">Draft</SelectItem>
                      <SelectItem value="submitted">Submitted</SelectItem>
                      <SelectItem value="reviewed">Reviewed</SelectItem>
                      <SelectItem value="approved">Approved</SelectItem>
                      <SelectItem value="needs_revision">Needs Revision</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select
                    value={filters.week_number?.toString() || 'all'}
                    onValueChange={(value) => handleFilterChange('week_number', value)}
                  >
                    <SelectTrigger className="w-full md:w-32">
                      <SelectValue placeholder="Week" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Weeks</SelectItem>
                      {Array.from({ length: 20 }, (_, i) => i + 1).map(week => (
                        <SelectItem key={week} value={week.toString()}>
                          Week {week}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>

            {/* Logbook List */}
            <LogbookList
              logbooks={logbooks}
              onEdit={handleEdit}
              onDelete={handleDelete}
              loading={loading}
              showStudentInfo={true}
            />
          </div>
        </div>
      </div>
  )
}
