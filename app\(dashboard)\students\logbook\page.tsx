"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { LogbookForm } from "@/features/logbook/components/logbook-form"
import { LogbookList } from "@/features/logbook/components/logbook-list"
import { Logbooks } from "@/features/logbook/server/Logbooks"
import { Logbook, StudentProgress } from "@/features/logbook/types/logbook"
import { useAuth } from "@/features/auth/hooks/use-auth"
import { toast } from "sonner"
import {
  Plus,
  BookOpen,
  Calendar,
  Clock,
  TrendingUp,
  FileText,
  CheckCircle,
  AlertCircle
} from "lucide-react"

export default function StudentLogbookPage() {
  const { user } = useAuth()
  const [logbooks, setLogbooks] = useState<Logbook[]>([])
  const [progress, setProgress] = useState<StudentProgress | null>(null)
  const [editingLogbook, setEditingLogbook] = useState<Logbook | null>(null)
  const [showForm, setShowForm] = useState(false)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)

  useEffect(() => {
    loadLogbooks()
    loadProgress()
  }, [])

  const loadLogbooks = async () => {
    try {
      setLoading(true)
      const result = await Logbooks.getMyLogbooks()
      setLogbooks(result?.data || [])
    } catch (error) {
      console.error("Failed to load logbooks:", error)
    } finally {
      setLoading(false)
    }
  }

  const loadProgress = async () => {
    if (!user?.id) return

    try {
      const result = await Logbooks.getStudentProgress(user.id.toString())
      setProgress(result?.data || null)
    } catch (error) {
      console.error("Failed to load progress:", error)
    }
  }

  const handleSave = async (logbook: Logbook) => {
    try {
      setSaving(true)
      await Logbooks.saveLogbook(logbook)
      await loadLogbooks()
      await loadProgress()
      setShowForm(false)
      setEditingLogbook(null)
    } catch (error) {
      console.error("Failed to save logbook:", error)
    } finally {
      setSaving(false)
    }
  }

  const handleSubmit = async (logbook: Logbook) => {
    try {
      setSaving(true)
      await Logbooks.saveLogbook(logbook)
      await loadLogbooks()
      await loadProgress()
      setShowForm(false)
      setEditingLogbook(null)
      toast.success("Logbook submitted successfully!")
    } catch (error) {
      console.error("Failed to submit logbook:", error)
    } finally {
      setSaving(false)
    }
  }

  const handleEdit = (logbook: Logbook) => {
    setEditingLogbook(logbook)
    setShowForm(true)
  }

  const handleDelete = async (id: string) => {
    try {
      await Logbooks.deleteLogbook(id)
      await loadLogbooks()
      await loadProgress()
      toast.success("Logbook deleted successfully")
    } catch (error) {
      console.error("Failed to delete logbook:", error)
    }
  }

  const handleNewLogbook = () => {
    setEditingLogbook(null)
    setShowForm(true)
  }

  const handleCancelForm = () => {
    setShowForm(false)
    setEditingLogbook(null)
  }

  const getNextWeekNumber = () => {
    if (logbooks.length === 0) return 1
    const maxWeek = Math.max(...logbooks.map(l => l.week_number))
    return maxWeek + 1
  }

  if (showForm) {
    return (
      <div className="flex flex-1 flex-col">
        <div className="@container/main flex flex-1 flex-col gap-2">
          <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
            <div className="flex flex-col gap-4 px-4 lg:px-6">
              <div className="flex items-center justify-between">
                <div className="flex flex-col gap-2">
                  <h1 className="text-2xl font-bold">
                    {editingLogbook ? 'Edit Logbook Entry' : 'New Logbook Entry'}
                  </h1>
                  <p className="text-muted-foreground">
                    {editingLogbook
                      ? 'Update your logbook entry with new information'
                      : 'Create a new weekly logbook entry to track your progress'
                    }
                  </p>
                </div>
                <Button variant="outline" onClick={handleCancelForm}>
                  Back to List
                </Button>
              </div>

              <LogbookForm
                initialData={editingLogbook || {
                  student_id: user?.id || "",
                  week_number: getNextWeekNumber(),
                  title: `Week ${getNextWeekNumber()} Summary`,
                  status: 'draft',
                  is_submitted: false
                }}
                onSave={handleSave}
                onSubmit={handleSubmit}
                loading={saving}
              />
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex flex-1 flex-col">
      <div className="@container/main flex flex-1 flex-col gap-2">
        <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
          <div className="flex flex-col gap-4 px-4 lg:px-6">
            <div className="flex items-center justify-between">
              <div className="flex flex-col gap-2">
                <h1 className="text-2xl font-bold">My Logbook</h1>
                <p className="text-muted-foreground">
                  Track your weekly progress and learning journey
                </p>
              </div>
              <Button onClick={handleNewLogbook} className="flex items-center gap-2">
                <Plus className="h-4 w-4" />
                New Entry
              </Button>
            </div>

            {/* Progress Overview */}
            {progress && (
              <div className="grid gap-4 md:grid-cols-4">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Total Entries</CardTitle>
                    <BookOpen className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{progress.total_entries}</div>
                    <p className="text-xs text-muted-foreground">
                      Week {progress.current_week} current
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Submitted</CardTitle>
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-green-600">{progress.submitted_entries}</div>
                    <p className="text-xs text-muted-foreground">
                      {((progress.submitted_entries / progress.total_entries) * 100).toFixed(0)}% completion
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Reviewed</CardTitle>
                    <FileText className="h-4 w-4 text-blue-500" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-blue-600">{progress.reviewed_entries}</div>
                    <p className="text-xs text-muted-foreground">
                      Supervisor feedback received
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Total Hours</CardTitle>
                    <Clock className="h-4 w-4 text-purple-500" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-purple-600">{progress.total_hours || 0}h</div>
                    <p className="text-xs text-muted-foreground">
                      Logged this period
                    </p>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Logbook List */}
            <LogbookList
              logbooks={logbooks}
              onEdit={handleEdit}
              onDelete={handleDelete}
              loading={loading}
            />
          </div>
        </div>
      </div>
    </div>
  )
}
