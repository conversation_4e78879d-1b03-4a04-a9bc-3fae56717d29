import axios from "axios"
import { toast } from "sonner"
import { Logbook, WeeklyReport, LogbookFilters, LogbookStatistics, StudentProgress } from "../types/logbook"

export class Logbooks {
  private static api_url = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8080/api"

  // Student logbook methods
  public static async getMyLogbooks() {
    try {
      const token = localStorage.getItem('token')
      const response = await axios.get(`${this.api_url}/logbooks/my-logbooks`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.status === 200) {
        return response.data
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to fetch logbooks")
      throw error.response?.data
    }
  }

  public static async getLogbook(id: string) {
    try {
      const token = localStorage.getItem('token')
      const response = await axios.get(`${this.api_url}/logbooks/logbook/${id}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.status === 200) {
        return response.data
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to fetch logbook")
      throw error.response?.data
    }
  }

  public static async saveLogbook(logbook: Logbook) {
    try {
      const token = localStorage.getItem('token')
      const response = await axios.post(`${this.api_url}/logbooks/logbook`, logbook, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (response.status === 200 || response.status === 201) {
        toast.success(response.data.message || "Logbook saved successfully")
        return response.data
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to save logbook")
      throw error.response?.data
    }
  }

  public static async updateLogbook(id: string, logbook: Partial<Logbook>) {
    try {
      const token = localStorage.getItem('token')
      const response = await axios.put(`${this.api_url}/logbooks/logbook/${id}`, logbook, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (response.status === 200) {
        toast.success(response.data.message || "Logbook updated successfully")
        return response.data
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to update logbook")
      throw error.response?.data
    }
  }

  public static async deleteLogbook(id: string) {
    try {
      const token = localStorage.getItem('token')
      const response = await axios.delete(`${this.api_url}/logbooks/logbook/${id}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.status === 200) {
        toast.success(response.data.message || "Logbook deleted successfully")
        return response.data
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to delete logbook")
      throw error.response?.data
    }
  }

  public static async getStudentProgress(studentId: string) {
    try {
      const token = localStorage.getItem('token')
      const response = await axios.get(`${this.api_url}/logbooks/progress/${studentId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.status === 200) {
        return response.data
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to fetch progress")
      throw error.response?.data
    }
  }

  // Weekly report methods
  public static async getMyWeeklyReports() {
    try {
      const token = localStorage.getItem('token')
      const response = await axios.get(`${this.api_url}/logbooks/my-weekly-reports`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.status === 200) {
        return response.data
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to fetch weekly reports")
      throw error.response?.data
    }
  }

  public static async getLogbookWeeklyReports(logbookId: string) {
    try {
      const token = localStorage.getItem('token')
      const response = await axios.get(`${this.api_url}/logbooks/logbook/${logbookId}/weekly-reports`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.status === 200) {
        return response.data
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to fetch weekly reports")
      throw error.response?.data
    }
  }

  public static async saveWeeklyReport(report: WeeklyReport) {
    try {
      const token = localStorage.getItem('token')
      const response = await axios.post(`${this.api_url}/logbooks/weekly-report`, report, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (response.status === 200 || response.status === 201) {
        toast.success(response.data.message || "Weekly report saved successfully")
        return response.data
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to save weekly report")
      throw error.response?.data
    }
  }

  public static async updateWeeklyReport(id: string, report: Partial<WeeklyReport>) {
    try {
      const token = localStorage.getItem('token')
      const response = await axios.put(`${this.api_url}/logbooks/weekly-report/${id}`, report, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (response.status === 200) {
        toast.success(response.data.message || "Weekly report updated successfully")
        return response.data
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to update weekly report")
      throw error.response?.data
    }
  }

  public static async deleteWeeklyReport(id: string) {
    try {
      const token = localStorage.getItem('token')
      const response = await axios.delete(`${this.api_url}/logbooks/weekly-report/${id}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.status === 200) {
        toast.success(response.data.message || "Weekly report deleted successfully")
        return response.data
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to delete weekly report")
      throw error.response?.data
    }
  }

  // Admin methods
  public static async getAllLogbooks(filters?: LogbookFilters) {
    try {
      const token = localStorage.getItem('token')
      const response = await axios.get(`${this.api_url}/admin/reports/logbooks`, {
        params: filters,
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.status === 200) {
        return response.data
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to fetch logbooks")
      throw error.response?.data
    }
  }

  public static async getAllWeeklyReports() {
    try {
      const token = localStorage.getItem('token')
      const response = await axios.get(`${this.api_url}/admin/reports/weekly-reports`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.status === 200) {
        return response.data
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to fetch weekly reports")
      throw error.response?.data
    }
  }

  public static async getLogbookStatistics() {
    try {
      const token = localStorage.getItem('token')
      const response = await axios.get(`${this.api_url}/admin/reports/logbook-statistics`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.status === 200) {
        return response.data
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to fetch statistics")
      throw error.response?.data
    }
  }

  // File upload method
  public static async uploadFile(file: File) {
    try {
      const token = localStorage.getItem('token')
      const formData = new FormData()
      formData.append('file', file)

      const response = await axios.post(`${this.api_url}/logbooks/upload`, formData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'multipart/form-data'
        }
      })

      if (response.status === 200) {
        return response.data
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to upload file")
      throw error.response?.data
    }
  }
}
