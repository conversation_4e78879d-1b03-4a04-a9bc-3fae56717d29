@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\03EM14\iptms\iptms_api\node_modules\.pnpm\nodemon@3.1.10\node_modules\nodemon\bin\node_modules;D:\03EM14\iptms\iptms_api\node_modules\.pnpm\nodemon@3.1.10\node_modules\nodemon\node_modules;D:\03EM14\iptms\iptms_api\node_modules\.pnpm\nodemon@3.1.10\node_modules;D:\03EM14\iptms\iptms_api\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\03EM14\iptms\iptms_api\node_modules\.pnpm\nodemon@3.1.10\node_modules\nodemon\bin\node_modules;D:\03EM14\iptms\iptms_api\node_modules\.pnpm\nodemon@3.1.10\node_modules\nodemon\node_modules;D:\03EM14\iptms\iptms_api\node_modules\.pnpm\nodemon@3.1.10\node_modules;D:\03EM14\iptms\iptms_api\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\..\..\..\nodemon@3.1.10\node_modules\nodemon\bin\nodemon.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\..\..\..\nodemon@3.1.10\node_modules\nodemon\bin\nodemon.js" %*
)
