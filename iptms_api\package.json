{"name": "iptms_api", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "seed": "node seeders/adminSeeder.js"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.6.2", "dependencies": {"bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "body-parser": "^2.2.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "iptms_api": "file:", "jsonwebtoken": "^9.0.2", "mysql": "^2.18.1", "mysql2": "^3.14.1", "nodemailer": "^6.10.1", "nodemon": "^3.1.10", "path": "^0.12.7", "pg": "^8.16.0", "pnpm": "^10.9.0"}}