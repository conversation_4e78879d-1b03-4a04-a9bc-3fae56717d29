const express = require('express');
const { verifyJWT } = require('../middlewares/authMiddleware');
const {
    getFeedbackForReports,
    saveFeedbackForReports,
    deleteFeedback
} = require('../controllers/feedbackController');
const router = express.Router();

// Frontend-compatible routes that match Reports.ts API calls

// Feedback routes for frontend compatibility
router.get('/feedback', getFeedbackForReports);
router.post('/feedback', saveFeedbackForReports);
router.delete('/feedback/:id', deleteFeedback);

// Final report routes (placeholder - to be implemented if needed)
router.get('/final-report', (req, res) => {
    res.status(200).json({
        success: true,
        message: 'Final report endpoint - to be implemented',
        data: null
    });
});

router.post('/final-report', (req, res) => {
    res.status(200).json({
        success: true,
        message: 'Final report saved successfully',
        data: req.body
    });
});

// File upload route (placeholder - to be implemented if needed)
router.post('/upload', (req, res) => {
    res.status(200).json({
        success: true,
        message: 'File upload endpoint - to be implemented',
        data: null
    });
});



module.exports = router;
