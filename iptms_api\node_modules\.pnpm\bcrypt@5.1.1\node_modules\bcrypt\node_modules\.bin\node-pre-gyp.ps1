#!/usr/bin/env pwsh
$basedir=Split-Path $MyInvocation.MyCommand.Definition -Parent

$exe=""
$pathsep=":"
$env_node_path=$env:NODE_PATH
$new_node_path="D:\03EM14\iptms\iptms_api\node_modules\.pnpm\@mapbox+node-pre-gyp@1.0.11\node_modules\@mapbox\node-pre-gyp\bin\node_modules;D:\03EM14\iptms\iptms_api\node_modules\.pnpm\@mapbox+node-pre-gyp@1.0.11\node_modules\@mapbox\node-pre-gyp\node_modules;D:\03EM14\iptms\iptms_api\node_modules\.pnpm\@mapbox+node-pre-gyp@1.0.11\node_modules\@mapbox\node_modules;D:\03EM14\iptms\iptms_api\node_modules\.pnpm\@mapbox+node-pre-gyp@1.0.11\node_modules;D:\03EM14\iptms\iptms_api\node_modules\.pnpm\node_modules"
if ($PSVersionTable.PSVersion -lt "6.0" -or $IsWindows) {
  # Fix case when both the Windows and Linux builds of Node
  # are installed in the same directory
  $exe=".exe"
  $pathsep=";"
} else {
  $new_node_path="/mnt/d/03EM14/iptms/iptms_api/node_modules/.pnpm/@mapbox+node-pre-gyp@1.0.11/node_modules/@mapbox/node-pre-gyp/bin/node_modules:/mnt/d/03EM14/iptms/iptms_api/node_modules/.pnpm/@mapbox+node-pre-gyp@1.0.11/node_modules/@mapbox/node-pre-gyp/node_modules:/mnt/d/03EM14/iptms/iptms_api/node_modules/.pnpm/@mapbox+node-pre-gyp@1.0.11/node_modules/@mapbox/node_modules:/mnt/d/03EM14/iptms/iptms_api/node_modules/.pnpm/@mapbox+node-pre-gyp@1.0.11/node_modules:/mnt/d/03EM14/iptms/iptms_api/node_modules/.pnpm/node_modules"
}
if ([string]::IsNullOrEmpty($env_node_path)) {
  $env:NODE_PATH=$new_node_path
} else {
  $env:NODE_PATH="$new_node_path$pathsep$env_node_path"
}

$ret=0
if (Test-Path "$basedir/node$exe") {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "$basedir/node$exe"  "$basedir/../../../../../@mapbox+node-pre-gyp@1.0.11/node_modules/@mapbox/node-pre-gyp/bin/node-pre-gyp" $args
  } else {
    & "$basedir/node$exe"  "$basedir/../../../../../@mapbox+node-pre-gyp@1.0.11/node_modules/@mapbox/node-pre-gyp/bin/node-pre-gyp" $args
  }
  $ret=$LASTEXITCODE
} else {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "node$exe"  "$basedir/../../../../../@mapbox+node-pre-gyp@1.0.11/node_modules/@mapbox/node-pre-gyp/bin/node-pre-gyp" $args
  } else {
    & "node$exe"  "$basedir/../../../../../@mapbox+node-pre-gyp@1.0.11/node_modules/@mapbox/node-pre-gyp/bin/node-pre-gyp" $args
  }
  $ret=$LASTEXITCODE
}
$env:NODE_PATH=$env_node_path
exit $ret
