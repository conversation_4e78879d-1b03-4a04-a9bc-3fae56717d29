const DailyLog = require('../models/DailyLog');
const WeeklyReport = require('../models/WeeklyReport');

// Get all logbooks (placeholder for backward compatibility)
const getAllLogbooks = async (req, res) => {
    try {
        res.status(200).json({
            success: true,
            message: 'Please use /api/admin/reports/ipt-daily-logs for IPT logbook data',
            data: []
        });
    } catch (error) {
        console.error('Error fetching all daily logs:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch daily logs',
            error: error.message
        });
    }
};

// Get single daily log by ID
const getSingleLogbook = async (req, res) => {
    try {
        const { id } = req.params;
        const logbook = await Logbook.show(id);
        
        if (!logbook) {
            return res.status(404).json({
                success: false,
                message: 'Logbook not found'
            });
        }
        
        res.status(200).json({
            success: true,
            data: logbook
        });
    } catch (error) {
        console.error('Error fetching logbook:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch logbook',
            error: error.message
        });
    }
};

// Get logbooks for a specific student
const getStudentLogbooks = async (req, res) => {
    try {
        const { student_id } = req.params;
        const logbooks = await Logbook.getByStudentId(student_id);
        
        res.status(200).json({
            success: true,
            data: logbooks
        });
    } catch (error) {
        console.error('Error fetching student logbooks:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch student logbooks',
            error: error.message
        });
    }
};

// Get logbooks for current user (student)
const getMyLogbooks = async (req, res) => {
    try {
        const { user_id } = req.user;
        const logbooks = await Logbook.getByStudentId(user_id);
        
        res.status(200).json({
            success: true,
            data: logbooks
        });
    } catch (error) {
        console.error('Error fetching user logbooks:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch your logbooks',
            error: error.message
        });
    }
};

// Create new logbook entry
const createLogbook = async (req, res) => {
    try {
        const { user_id } = req.user;
        const logbookData = {
            ...req.body,
            student_id: user_id
        };
        
        const logbookId = await Logbook.create(logbookData);
        const newLogbook = await Logbook.show(logbookId);
        
        res.status(201).json({
            success: true,
            message: 'Logbook created successfully',
            data: newLogbook
        });
    } catch (error) {
        console.error('Error creating logbook:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to create logbook',
            error: error.message
        });
    }
};

// Update existing logbook entry
const updateLogbook = async (req, res) => {
    try {
        const { id } = req.params;
        const { user_id } = req.user;
        
        // Check if logbook exists and belongs to user
        const existingLogbook = await Logbook.show(id);
        if (!existingLogbook) {
            return res.status(404).json({
                success: false,
                message: 'Logbook not found'
            });
        }
        
        if (existingLogbook.student_id !== user_id) {
            return res.status(403).json({
                success: false,
                message: 'You can only update your own logbooks'
            });
        }
        
        const updated = await Logbook.update(id, req.body);
        
        if (!updated) {
            return res.status(400).json({
                success: false,
                message: 'Failed to update logbook'
            });
        }
        
        const updatedLogbook = await Logbook.show(id);
        
        res.status(200).json({
            success: true,
            message: 'Logbook updated successfully',
            data: updatedLogbook
        });
    } catch (error) {
        console.error('Error updating logbook:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update logbook',
            error: error.message
        });
    }
};

// Delete logbook entry
const deleteLogbook = async (req, res) => {
    try {
        const { id } = req.params;
        const { user_id } = req.user;
        
        // Check if logbook exists and belongs to user (or user is admin)
        const existingLogbook = await Logbook.show(id);
        if (!existingLogbook) {
            return res.status(404).json({
                success: false,
                message: 'Logbook not found'
            });
        }
        
        // Allow deletion if user owns the logbook or is admin
        if (existingLogbook.student_id !== user_id && req.user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: 'You can only delete your own logbooks'
            });
        }
        
        const deleted = await Logbook.delete(id);
        
        if (!deleted) {
            return res.status(400).json({
                success: false,
                message: 'Failed to delete logbook'
            });
        }
        
        res.status(200).json({
            success: true,
            message: 'Logbook deleted successfully'
        });
    } catch (error) {
        console.error('Error deleting logbook:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to delete logbook',
            error: error.message
        });
    }
};

// Save logbook (create or update based on ID presence)
const saveLogbook = async (req, res) => {
    try {
        const { user_id } = req.user;
        const data = { ...req.body, student_id: user_id };
        
        if (data.id) {
            // Update existing logbook
            const existingLogbook = await Logbook.show(data.id);
            if (!existingLogbook) {
                return res.status(404).json({
                    success: false,
                    message: 'Logbook not found'
                });
            }
            
            if (existingLogbook.student_id !== user_id) {
                return res.status(403).json({
                    success: false,
                    message: 'You can only update your own logbooks'
                });
            }
            
            const updated = await Logbook.update(data.id, data);
            if (!updated) {
                return res.status(400).json({
                    success: false,
                    message: 'Failed to update logbook'
                });
            }
            
            const updatedLogbook = await Logbook.show(data.id);
            return res.status(200).json({
                success: true,
                message: 'Logbook updated successfully',
                data: updatedLogbook
            });
        } else {
            // Create new logbook
            const logbookId = await Logbook.create(data);
            const newLogbook = await Logbook.show(logbookId);
            
            return res.status(201).json({
                success: true,
                message: 'Logbook created successfully',
                data: newLogbook
            });
        }
    } catch (error) {
        console.error('Error saving logbook:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to save logbook',
            error: error.message
        });
    }
};

// Update supervisor feedback
const updateSupervisorFeedback = async (req, res) => {
    try {
        const { id } = req.params;
        const { feedback } = req.body;
        const { user_id } = req.user;
        
        const updated = await Logbook.updateSupervisorFeedback(id, feedback, user_id);
        
        if (!updated) {
            return res.status(400).json({
                success: false,
                message: 'Failed to update supervisor feedback'
            });
        }
        
        const updatedLogbook = await Logbook.show(id);
        
        res.status(200).json({
            success: true,
            message: 'Supervisor feedback updated successfully',
            data: updatedLogbook
        });
    } catch (error) {
        console.error('Error updating supervisor feedback:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update supervisor feedback',
            error: error.message
        });
    }
};

// Get logbook statistics (admin only)
const getLogbookStatistics = async (req, res) => {
    try {
        const stats = await DailyLog.getStatistics();
        
        res.status(200).json({
            success: true,
            data: stats
        });
    } catch (error) {
        console.error('Error fetching logbook statistics:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch logbook statistics',
            error: error.message
        });
    }
};

// Get student progress
const getStudentProgress = async (req, res) => {
    try {
        const { student_id } = req.params;
        const progress = await Logbook.getStudentProgress(student_id);
        
        res.status(200).json({
            success: true,
            data: progress
        });
    } catch (error) {
        console.error('Error fetching student progress:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch student progress',
            error: error.message
        });
    }
};

// Weekly Report Controllers

// Get all weekly reports (admin only)
const getAllWeeklyReports = async (req, res) => {
    try {
        const reports = await WeeklyReport.index();

        res.status(200).json({
            success: true,
            data: reports
        });
    } catch (error) {
        console.error('Error fetching all weekly reports:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch weekly reports',
            error: error.message
        });
    }
};

// Get weekly reports for a logbook
const getLogbookWeeklyReports = async (req, res) => {
    try {
        const { logbook_id } = req.params;
        const reports = await WeeklyReport.getByLogbookId(logbook_id);

        res.status(200).json({
            success: true,
            data: reports
        });
    } catch (error) {
        console.error('Error fetching logbook weekly reports:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch weekly reports',
            error: error.message
        });
    }
};

// Get weekly reports for current user
const getMyWeeklyReports = async (req, res) => {
    try {
        const { user_id } = req.user;
        const reports = await WeeklyReport.getByStudentId(user_id);

        res.status(200).json({
            success: true,
            data: reports
        });
    } catch (error) {
        console.error('Error fetching user weekly reports:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch your weekly reports',
            error: error.message
        });
    }
};

// Create weekly report
const createWeeklyReport = async (req, res) => {
    try {
        const { user_id } = req.user;
        const reportData = {
            ...req.body,
            student_id: user_id
        };

        const reportId = await WeeklyReport.create(reportData);
        const newReport = await WeeklyReport.show(reportId);

        res.status(201).json({
            success: true,
            message: 'Weekly report created successfully',
            data: newReport
        });
    } catch (error) {
        console.error('Error creating weekly report:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to create weekly report',
            error: error.message
        });
    }
};

// Update weekly report
const updateWeeklyReport = async (req, res) => {
    try {
        const { id } = req.params;
        const { user_id } = req.user;

        const existingReport = await WeeklyReport.show(id);
        if (!existingReport) {
            return res.status(404).json({
                success: false,
                message: 'Weekly report not found'
            });
        }

        if (existingReport.student_id !== user_id) {
            return res.status(403).json({
                success: false,
                message: 'You can only update your own weekly reports'
            });
        }

        const updated = await WeeklyReport.update(id, req.body);

        if (!updated) {
            return res.status(400).json({
                success: false,
                message: 'Failed to update weekly report'
            });
        }

        const updatedReport = await WeeklyReport.show(id);

        res.status(200).json({
            success: true,
            message: 'Weekly report updated successfully',
            data: updatedReport
        });
    } catch (error) {
        console.error('Error updating weekly report:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update weekly report',
            error: error.message
        });
    }
};

// Delete weekly report
const deleteWeeklyReport = async (req, res) => {
    try {
        const { id } = req.params;
        const { user_id } = req.user;

        const existingReport = await WeeklyReport.show(id);
        if (!existingReport) {
            return res.status(404).json({
                success: false,
                message: 'Weekly report not found'
            });
        }

        if (existingReport.student_id !== user_id && req.user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: 'You can only delete your own weekly reports'
            });
        }

        const deleted = await WeeklyReport.delete(id);

        if (!deleted) {
            return res.status(400).json({
                success: false,
                message: 'Failed to delete weekly report'
            });
        }

        res.status(200).json({
            success: true,
            message: 'Weekly report deleted successfully'
        });
    } catch (error) {
        console.error('Error deleting weekly report:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to delete weekly report',
            error: error.message
        });
    }
};

// Update supervisor feedback for weekly report
const updateWeeklyReportSupervisorFeedback = async (req, res) => {
    try {
        const { id } = req.params;
        const { rating, comments } = req.body;

        const updated = await WeeklyReport.updateSupervisorFeedback(id, rating, comments);

        if (!updated) {
            return res.status(400).json({
                success: false,
                message: 'Failed to update supervisor feedback'
            });
        }

        const updatedReport = await WeeklyReport.show(id);

        res.status(200).json({
            success: true,
            message: 'Supervisor feedback updated successfully',
            data: updatedReport
        });
    } catch (error) {
        console.error('Error updating supervisor feedback:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update supervisor feedback',
            error: error.message
        });
    }
};

module.exports = {
    getAllLogbooks,
    getSingleLogbook,
    getStudentLogbooks,
    getMyLogbooks,
    createLogbook,
    updateLogbook,
    deleteLogbook,
    saveLogbook,
    updateSupervisorFeedback,
    getLogbookStatistics,
    getStudentProgress,
    getAllWeeklyReports,
    getLogbookWeeklyReports,
    getMyWeeklyReports,
    createWeeklyReport,
    updateWeeklyReport,
    deleteWeeklyReport,
    updateWeeklyReportSupervisorFeedback
};
