const express = require('express');
const { verifyJWT } = require('../middlewares/authMiddleware');
const {
    getAllDailyLogs,
    getMyDailyLogs,
    getWeekDailyLogs,
    createDailyLog,
    updateDailyLog,
    deleteDailyLog,
    getAllWeeklyReports,
    getMyWeeklyReports,
    getWeeklyReport,
    createWeeklyReport,
    updateWeeklyReport
} = require('../controllers/iptLogbookController');

const router = express.Router();

// Daily Log Routes (Student)
router.get('/daily-logs', verifyJWT, getMyDailyLogs);
router.get('/daily-logs/week/:week_number', verifyJWT, getWeekDailyLogs);
router.post('/daily-log', verifyJWT, createDailyLog);
router.put('/daily-log/:id', verifyJWT, updateDailyLog);
router.delete('/daily-log/:id', verifyJWT, deleteDailyLog);

// Weekly Report Routes (Student)
router.get('/weekly-reports', verifyJWT, getMyWeeklyReports);
router.get('/weekly-report/week/:week_number', verifyJWT, getWeeklyReport);
router.post('/weekly-report', verifyJWT, createWeeklyReport);
router.put('/weekly-report/:id', verifyJWT, updateWeeklyReport);

// Admin Routes
router.get('/admin/daily-logs', verifyJWT, getAllDailyLogs);
router.get('/admin/weekly-reports', verifyJWT, getAllWeeklyReports);

// File upload route (placeholder)
router.post('/upload', verifyJWT, (req, res) => {
    res.status(200).json({
        success: true,
        message: 'File upload endpoint - to be implemented',
        data: null
    });
});

module.exports = router;
