const axios = require('axios');

async function testSingleFeedback() {
    try {
        console.log('Testing single feedback creation...');
        
        const feedbackData = {
            student_id: 17, // Using user_id 17 which has a student record
            category: 'general',
            title: 'Debug Test Feedback',
            submission_type: 'write',
            content: 'This is a debug test feedback content',
            character_count: 40,
            rating: 4,
            is_submitted: true
        };

        console.log('Sending data:', feedbackData);

        const response = await axios.post('http://localhost:8080/api/reports/feedback', feedbackData);
        
        console.log('Response status:', response.status);
        console.log('Response data:', JSON.stringify(response.data, null, 2));
        
    } catch (error) {
        console.error('Error:', error.response?.data || error.message);
    }
}

testSingleFeedback();
