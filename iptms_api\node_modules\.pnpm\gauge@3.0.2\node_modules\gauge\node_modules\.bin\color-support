#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/03EM14/iptms/iptms_api/node_modules/.pnpm/color-support@1.1.3/node_modules/color-support/node_modules:/mnt/d/03EM14/iptms/iptms_api/node_modules/.pnpm/color-support@1.1.3/node_modules:/mnt/d/03EM14/iptms/iptms_api/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/03EM14/iptms/iptms_api/node_modules/.pnpm/color-support@1.1.3/node_modules/color-support/node_modules:/mnt/d/03EM14/iptms/iptms_api/node_modules/.pnpm/color-support@1.1.3/node_modules:/mnt/d/03EM14/iptms/iptms_api/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../color-support@1.1.3/node_modules/color-support/bin.js" "$@"
else
  exec node  "$basedir/../../../../../color-support@1.1.3/node_modules/color-support/bin.js" "$@"
fi
