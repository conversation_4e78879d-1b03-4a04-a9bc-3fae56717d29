#!/usr/bin/env pwsh
$basedir=Split-Path $MyInvocation.MyCommand.Definition -Parent

$exe=""
$pathsep=":"
$env_node_path=$env:NODE_PATH
$new_node_path="D:\03EM14\iptms\iptms_api\node_modules\.pnpm\nopt@5.0.0\node_modules\nopt\bin\node_modules;D:\03EM14\iptms\iptms_api\node_modules\.pnpm\nopt@5.0.0\node_modules\nopt\node_modules;D:\03EM14\iptms\iptms_api\node_modules\.pnpm\nopt@5.0.0\node_modules;D:\03EM14\iptms\iptms_api\node_modules\.pnpm\node_modules"
if ($PSVersionTable.PSVersion -lt "6.0" -or $IsWindows) {
  # Fix case when both the Windows and Linux builds of Node
  # are installed in the same directory
  $exe=".exe"
  $pathsep=";"
} else {
  $new_node_path="/mnt/d/03EM14/iptms/iptms_api/node_modules/.pnpm/nopt@5.0.0/node_modules/nopt/bin/node_modules:/mnt/d/03EM14/iptms/iptms_api/node_modules/.pnpm/nopt@5.0.0/node_modules/nopt/node_modules:/mnt/d/03EM14/iptms/iptms_api/node_modules/.pnpm/nopt@5.0.0/node_modules:/mnt/d/03EM14/iptms/iptms_api/node_modules/.pnpm/node_modules"
}
if ([string]::IsNullOrEmpty($env_node_path)) {
  $env:NODE_PATH=$new_node_path
} else {
  $env:NODE_PATH="$new_node_path$pathsep$env_node_path"
}

$ret=0
if (Test-Path "$basedir/node$exe") {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "$basedir/node$exe"  "$basedir/../../../../../../nopt@5.0.0/node_modules/nopt/bin/nopt.js" $args
  } else {
    & "$basedir/node$exe"  "$basedir/../../../../../../nopt@5.0.0/node_modules/nopt/bin/nopt.js" $args
  }
  $ret=$LASTEXITCODE
} else {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "node$exe"  "$basedir/../../../../../../nopt@5.0.0/node_modules/nopt/bin/nopt.js" $args
  } else {
    & "node$exe"  "$basedir/../../../../../../nopt@5.0.0/node_modules/nopt/bin/nopt.js" $args
  }
  $ret=$LASTEXITCODE
}
$env:NODE_PATH=$env_node_path
exit $ret
