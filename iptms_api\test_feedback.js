const axios = require('axios');

async function testAllFeedbackOperations() {
    try {
        console.log('🚀 Testing all feedback operations...\n');

        // Test 1: Create multiple feedback entries
        console.log('1. Creating test feedback entries...');
        const feedbackEntries = [
            {
                student_id: 16,
                category: 'general',
                title: 'General Feedback',
                submission_type: 'write',
                content: 'This is general feedback about the program',
                character_count: 45,
                rating: 4,
                is_submitted: true
            },
            {
                student_id: 16,
                category: 'program',
                title: 'Program Structure Feedback',
                submission_type: 'write',
                content: 'The program structure could be improved in several areas',
                character_count: 60,
                rating: 3,
                is_submitted: true
            },
            {
                student_id: 16,
                category: 'supervision',
                title: 'Supervision Quality',
                submission_type: 'write',
                content: 'Excellent supervision provided throughout the program',
                character_count: 55,
                rating: 5,
                is_submitted: false
            }
        ];

        const createdFeedback = [];
        for (let i = 0; i < feedbackEntries.length; i++) {
            const response = await axios.post('http://localhost:8080/api/reports/feedback', feedbackEntries[i]);
            if (response.data.success) {
                console.log(`   ✅ Created feedback ${i + 1}: ${feedbackEntries[i].title}`);
                createdFeedback.push(response.data.data);
            } else {
                console.log(`   ❌ Failed to create feedback ${i + 1}`);
            }
        }

        // Test 2: Fetch all feedback
        console.log('\n2. Fetching all feedback...');
        const allFeedbackResponse = await axios.get('http://localhost:8080/api/reports/feedback');
        if (allFeedbackResponse.data.success) {
            console.log(`   ✅ Retrieved ${allFeedbackResponse.data.data.length} feedback entries`);
            console.log('   📋 Feedback titles:', allFeedbackResponse.data.data.map(f => f.title));
        } else {
            console.log('   ❌ Failed to fetch all feedback');
        }

        // Test 3: Fetch single feedback (if we have any)
        if (createdFeedback.length > 0) {
            console.log('\n3. Fetching single feedback...');
            const feedbackId = createdFeedback[0].id;
            const singleResponse = await axios.get(`http://localhost:8080/api/other/getFeedback/${feedbackId}`);
            if (singleResponse.data.success) {
                console.log(`   ✅ Retrieved single feedback: ${singleResponse.data.data[0]?.title}`);
            } else {
                console.log('   ❌ Failed to fetch single feedback');
            }
        }

        // Test 4: Update feedback
        if (createdFeedback.length > 0) {
            console.log('\n4. Updating feedback...');
            const feedbackId = createdFeedback[0].id;
            const updateData = {
                title: 'Updated General Feedback',
                content: 'This feedback has been updated',
                rating: 5
            };
            const updateResponse = await axios.put(`http://localhost:8080/api/other/updateFeedback/${feedbackId}`, updateData);
            if (updateResponse.data.success) {
                console.log('   ✅ Feedback updated successfully');
            } else {
                console.log('   ❌ Failed to update feedback');
            }
        }

        // Test 5: Get feedback by student
        console.log('\n5. Fetching feedback by student...');
        const studentFeedbackResponse = await axios.get('http://localhost:8080/api/other/getFeedbackByStudent/16');
        if (studentFeedbackResponse.data.success) {
            console.log(`   ✅ Retrieved ${studentFeedbackResponse.data.data.length} feedback entries for student`);
        } else {
            console.log('   ❌ Failed to fetch student feedback');
        }

        // Test 6: Get feedback by category
        console.log('\n6. Fetching feedback by category...');
        const categoryResponse = await axios.get('http://localhost:8080/api/other/getFeedbackByType/general');
        if (categoryResponse.data.success) {
            console.log(`   ✅ Retrieved ${categoryResponse.data.data.length} general feedback entries`);
        } else {
            console.log('   ❌ Failed to fetch feedback by category');
        }

        console.log('\n🎉 All tests completed!');

    } catch (error) {
        console.error('❌ Error during testing:', error.response?.data || error.message);
    }
}

testAllFeedbackOperations();
