const Student = require('../models/Student')

const getAllStudents = async (req,res) => {
    try {
        const result = await Student.index();

        res.status(200).json({
            success: true,
            data: result
        })
        
    } catch (error) {
        console.error(error)
        res.status(500).json({
            success: false,
            error: error
        })
    }
}

const getSingleStudent = async (req,res) => {
    const student_id = req.params.id;
    try {
        const student = await Student.index(student_id);

        res.status(200).json({
            success: true,
            data: student
        })
        
    } catch (error) {
        console.error(error)
        res.status(500).json({
            success: false,
            error: error
        })
    }
}

module.exports = {
    getAllStudents,
    getSingleStudent
}