const Feedback = require('./models/Feedback');

async function testDirectFeedback() {
    try {
        console.log('🚀 Testing feedback model directly...\n');
        
        // Test 1: Create feedback
        console.log('1. Creating feedback...');
        const feedbackData = {
            student_id: 17,
            category: 'general',
            title: 'Direct Test Feedback',
            submission_type: 'write',
            content: 'This is a direct test of the feedback model',
            character_count: 45,
            rating: 4,
            is_submitted: true
        };

        const createdFeedback = await Feedback.create(feedbackData);
        console.log('✅ Created feedback:', createdFeedback);

        // Test 2: Get all feedback
        console.log('\n2. Getting all feedback...');
        const allFeedback = await Feedback.index();
        console.log(`✅ Retrieved ${allFeedback.length} feedback entries`);
        if (allFeedback.length > 0) {
            console.log('First feedback:', allFeedback[0]);
        }

        // Test 3: Get single feedback
        if (createdFeedback && createdFeedback.id) {
            console.log('\n3. Getting single feedback...');
            const singleFeedback = await Feedback.show(createdFeedback.id);
            console.log('✅ Retrieved single feedback:', singleFeedback[0]);
        }

        // Test 4: Update feedback
        if (createdFeedback && createdFeedback.id) {
            console.log('\n4. Updating feedback...');
            const updatedFeedback = await Feedback.update(createdFeedback.id, {
                title: 'Updated Direct Test Feedback',
                rating: 5
            });
            console.log('✅ Updated feedback:', updatedFeedback);
        }

        // Test 5: Get by student
        console.log('\n5. Getting feedback by student...');
        const studentFeedback = await Feedback.getByStudent(17);
        console.log(`✅ Retrieved ${studentFeedback.length} feedback entries for student 17`);

        console.log('\n🎉 All direct tests completed successfully!');
        process.exit(0);
        
    } catch (error) {
        console.error('❌ Error during direct testing:', error);
        process.exit(1);
    }
}

testDirectFeedback();
