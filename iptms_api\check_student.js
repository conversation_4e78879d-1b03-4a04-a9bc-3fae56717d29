const db = require('./config/db');

async function checkStudent() {
    try {
        console.log('Checking if student_id 16 exists...');
        
        const [users] = await db.query('SELECT * FROM users WHERE id = ?', [16]);
        console.log('User with ID 16:', users[0]);
        
        if (users[0]) {
            const [students] = await db.query('SELECT * FROM students WHERE user_id = ?', [16]);
            console.log('Student record:', students[0]);
        }
        
        // Also check all users to see what IDs exist
        const [allUsers] = await db.query('SELECT id, first_name, last_name FROM users LIMIT 10');
        console.log('Available users:', allUsers);

        // Check students table structure
        const [allStudents] = await db.query('SELECT * FROM students LIMIT 5');
        console.log('Students table data:', allStudents);
        
        process.exit(0);
    } catch (error) {
        console.error('Error:', error);
        process.exit(1);
    }
}

checkStudent();
