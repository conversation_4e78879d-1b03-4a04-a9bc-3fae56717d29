'use client'

import { use<PERSON>tate, useEffect } from "react"
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Download,
  Search,
  Filter,
  MessageSquare,
  Calendar,
  User,
  Star,
  Upload,
  Edit3,
  Eye,
  ExternalLink,
  TrendingUp,
  Trash2,
  AlertTriangle
} from "lucide-react"
import { Reports } from "@/features/reports/server/Reports"
import { Feedback, AdminReportFilters } from "@/features/reports/types/reports"
import { ConfirmationDialog } from "@/features/logbook/components/confirmation-dialog"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/dialog"
import { toast } from "sonner"

interface FeedbackWithStudent extends Feedback {
  student_name: string
  student_email: string
  program_name?: string
  supervisor_name?: string
}

interface AdminFilePreviewDialogProps {
  isOpen: boolean
  onClose: () => void
  feedback: FeedbackWithStudent | null
}

function AdminFilePreviewDialog({ isOpen, onClose, feedback }: AdminFilePreviewDialogProps) {
  if (!feedback) return null

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const handleDownload = () => {
    if (feedback.file_url) {
      const link = document.createElement('a')
      link.href = feedback.file_url
      link.download = feedback.file_name || 'feedback-file'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            File Preview: {feedback.file_name}
          </DialogTitle>
          <p className="text-sm text-muted-foreground">
            Submitted by {feedback.student_name} ({feedback.student_email})
          </p>
        </DialogHeader>

        <div className="space-y-4">
          {/* File Info */}
          <div className="flex items-center justify-between p-4 bg-muted rounded-lg">
            <div className="space-y-1">
              <p className="font-medium">{feedback.file_name}</p>
              <p className="text-sm text-muted-foreground">
                {feedback.file_type} • {feedback.file_size ? formatFileSize(feedback.file_size) : 'Unknown size'}
              </p>
            </div>
            <Button onClick={handleDownload} variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Download
            </Button>
          </div>

          {/* File Preview */}
          <div className="border rounded-lg overflow-hidden" style={{ height: '500px' }}>
            {feedback.file_type === 'application/pdf' && feedback.file_url ? (
              <iframe
                src={feedback.file_url}
                className="w-full h-full"
                title="PDF Preview"
              />
            ) : (
              <div className="flex items-center justify-center h-full bg-muted">
                <div className="text-center">
                  <FileText className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
                  <p className="text-lg font-medium">Preview not available</p>
                  <p className="text-sm text-muted-foreground">
                    This file type cannot be previewed in the browser
                  </p>
                  <Button onClick={handleDownload} className="mt-4">
                    <Download className="h-4 w-4 mr-2" />
                    Download to view
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default function AdminFeedbackPage() {
  const [loading, setLoading] = useState(true)
  const [feedback, setFeedback] = useState<FeedbackWithStudent[]>([])
  const [filteredFeedback, setFilteredFeedback] = useState<FeedbackWithStudent[]>([])
  const [filters, setFilters] = useState<AdminReportFilters>({
    student_name: '',
    status: 'all',
    submission_type: 'all',
    program: '',
    supervisor: ''
  })
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [expandedFeedback, setExpandedFeedback] = useState<string | null>(null)
  const [sortBy, setSortBy] = useState<'date' | 'student' | 'category' | 'rating' | 'status'>('date')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const [deleteConfirmation, setDeleteConfirmation] = useState<{
    isOpen: boolean
    feedbackId: string | null
    feedbackTitle: string
    studentName: string
  }>({
    isOpen: false,
    feedbackId: null,
    feedbackTitle: "",
    studentName: ""
  })
  const [previewDialog, setPreviewDialog] = useState<{
    isOpen: boolean
    feedback: FeedbackWithStudent | null
  }>({
    isOpen: false,
    feedback: null
  })

  useEffect(() => {
    loadFeedback()
  }, [])

  useEffect(() => {
    applyFilters()
  }, [feedback, filters, selectedCategory, sortBy, sortOrder])

  const loadFeedback = async () => {
    setLoading(true)
    try {
      const result = await Reports.getAllFeedback()
      const data = result?.data || []
      setFeedback(data)
    } catch (error) {
      console.error("Failed to load feedback:", error)
      setFeedback([])
    } finally {
      setLoading(false)
    }
  }

  const applyFilters = () => {
    let filtered = [...feedback]

    if (filters.student_name) {
      filtered = filtered.filter(item => 
        item.student_name.toLowerCase().includes(filters.student_name!.toLowerCase())
      )
    }

    if (filters.status && filters.status !== 'all') {
      filtered = filtered.filter(item => {
        switch (filters.status) {
          case 'completed':
            return item.is_submitted
          case 'pending':
            return !item.is_submitted
          default:
            return true
        }
      })
    }

    if (filters.submission_type && filters.submission_type !== 'all') {
      filtered = filtered.filter(item => item.submission_type === filters.submission_type)
    }

    if (selectedCategory && selectedCategory !== 'all') {
      filtered = filtered.filter(item => item.category === selectedCategory)
    }

    if (filters.program) {
      filtered = filtered.filter(item => 
        item.program_name?.toLowerCase().includes(filters.program!.toLowerCase())
      )
    }

    if (filters.supervisor) {
      filtered = filtered.filter(item =>
        item.supervisor_name?.toLowerCase().includes(filters.supervisor!.toLowerCase())
      )
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue: any, bValue: any

      switch (sortBy) {
        case 'date':
          aValue = new Date(a.submitted_at || a.created_at || 0).getTime()
          bValue = new Date(b.submitted_at || b.created_at || 0).getTime()
          break
        case 'student':
          aValue = a.student_name.toLowerCase()
          bValue = b.student_name.toLowerCase()
          break
        case 'category':
          aValue = a.category
          bValue = b.category
          break
        case 'rating':
          aValue = a.rating || 0
          bValue = b.rating || 0
          break
        case 'status':
          aValue = a.is_submitted ? 1 : 0
          bValue = b.is_submitted ? 1 : 0
          break
        default:
          return 0
      }

      if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1
      if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1
      return 0
    })

    setFilteredFeedback(filtered)
  }

  const handleExport = async () => {
    try {
      await Reports.exportReports('feedback', 'excel', { ...filters, category: selectedCategory })
    } catch (error) {
      toast.error("Failed to export feedback")
    }
  }

  const getCategoryLabel = (category: string) => {
    const labels: Record<string, string> = {
      'general': 'General Experience',
      'program': 'Program Structure',
      'supervision': 'Supervision & Mentoring',
      'facilities': 'Facilities & Resources',
      'recommendations': 'Recommendations'
    }
    return labels[category] || category
  }

  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      'general': 'bg-blue-100 text-blue-800',
      'program': 'bg-green-100 text-green-800',
      'supervision': 'bg-purple-100 text-purple-800',
      'facilities': 'bg-orange-100 text-orange-800',
      'recommendations': 'bg-pink-100 text-pink-800'
    }
    return colors[category] || 'bg-gray-100 text-gray-800'
  }

  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`h-4 w-4 ${
              star <= rating ? 'text-yellow-500 fill-current' : 'text-gray-300'
            }`}
          />
        ))}
        <span className="ml-1 text-sm text-muted-foreground">({rating}/5)</span>
      </div>
    )
  }

  const getStatusBadge = (item: FeedbackWithStudent) => {
    if (item.is_submitted) {
      return <Badge className="bg-green-100 text-green-800">Submitted</Badge>
    } else {
      return <Badge variant="outline" className="border-orange-500 text-orange-700">Draft</Badge>
    }
  }

  const toggleFeedbackExpansion = (feedbackId: string) => {
    setExpandedFeedback(expandedFeedback === feedbackId ? null : feedbackId)
  }

  const handleDeleteFeedback = (feedback: FeedbackWithStudent) => {
    setDeleteConfirmation({
      isOpen: true,
      feedbackId: feedback.id!,
      feedbackTitle: feedback.title,
      studentName: feedback.student_name
    })
  }

  const confirmDeleteFeedback = async () => {
    if (!deleteConfirmation.feedbackId) return

    try {
      await Reports.deleteFeedback(deleteConfirmation.feedbackId)
      setFeedback(prev => prev.filter(f => f.id !== deleteConfirmation.feedbackId))
      toast.success("Feedback deleted successfully")
    } catch (error) {
      toast.error("Failed to delete feedback")
      console.error("Delete error:", error)
    } finally {
      setDeleteConfirmation({
        isOpen: false,
        feedbackId: null,
        feedbackTitle: "",
        studentName: ""
      })
    }
  }

  const cancelDeleteFeedback = () => {
    setDeleteConfirmation({
      isOpen: false,
      feedbackId: null,
      feedbackTitle: "",
      studentName: ""
    })
  }

  const handlePreviewFile = (feedback: FeedbackWithStudent) => {
    setPreviewDialog({
      isOpen: true,
      feedback
    })
  }

  const closePreview = () => {
    setPreviewDialog({
      isOpen: false,
      feedback: null
    })
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // Calculate statistics
  const stats = {
    total: feedback.length,
    submitted: feedback.filter(f => f.is_submitted).length,
    drafts: feedback.filter(f => !f.is_submitted).length,
    averageRating: feedback.length > 0 
      ? feedback.reduce((sum, f) => sum + (f.rating || 0), 0) / feedback.length 
      : 0
  }

  const categoryStats = ['general', 'program', 'supervision', 'facilities', 'recommendations'].map(cat => ({
    category: cat,
    label: getCategoryLabel(cat),
    count: feedback.filter(f => f.category === cat).length,
    averageRating: feedback.filter(f => f.category === cat).length > 0
      ? feedback.filter(f => f.category === cat).reduce((sum, f) => sum + (f.rating || 0), 0) / feedback.filter(f => f.category === cat).length
      : 0
  }))

  if (loading) {
    return (
      <div className="h-full flex-1 flex-col space-y-4 p-4 md:space-y-8 md:p-8 flex">
        <div className="flex items-center justify-center h-40">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading feedback...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex-1 flex-col space-y-4 p-4 md:space-y-8 md:p-8 flex">
      <div className="flex items-center justify-between space-y-2">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Feedback & Recommendations</h2>
          <p className="text-muted-foreground">
            Review and analyze all student feedback submissions
          </p>
        </div>
        
        <Button onClick={handleExport}>
          <Download className="mr-2 h-4 w-4" />
          Export Feedback
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Feedback</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Submitted</CardTitle>
            <MessageSquare className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.submitted}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Drafts</CardTitle>
            <Edit3 className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{stats.drafts}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Rating</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.averageRating.toFixed(1)}/5</div>
          </CardContent>
        </Card>
      </div>

      {/* Category Statistics */}
      <Card>
        <CardHeader>
          <CardTitle>Feedback by Category</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-5">
            {categoryStats.map((stat) => (
              <div key={stat.category} className="text-center p-3 border rounded-lg">
                <Badge className={getCategoryColor(stat.category)} variant="secondary">
                  {stat.label}
                </Badge>
                <div className="mt-2">
                  <div className="text-2xl font-bold">{stat.count}</div>
                  <div className="text-sm text-muted-foreground">
                    Avg: {stat.averageRating.toFixed(1)}/5
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Filter className="h-5 w-5" />
            <span>Filters</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
            <div className="space-y-2">
              <Label htmlFor="student-search">Student Name</Label>
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  id="student-search"
                  placeholder="Search students..."
                  value={filters.student_name || ''}
                  onChange={(e) => setFilters(prev => ({ ...prev, student_name: e.target.value }))}
                  className="pl-8"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="category-filter">Category</Label>
              <Select
                value={selectedCategory}
                onValueChange={setSelectedCategory}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All categories" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  <SelectItem value="general">General Experience</SelectItem>
                  <SelectItem value="program">Program Structure</SelectItem>
                  <SelectItem value="supervision">Supervision & Mentoring</SelectItem>
                  <SelectItem value="facilities">Facilities & Resources</SelectItem>
                  <SelectItem value="recommendations">Recommendations</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="status-filter">Status</Label>
              <Select
                value={filters.status || 'all'}
                onValueChange={(value) => setFilters(prev => ({ ...prev, status: value as any }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="completed">Submitted</SelectItem>
                  <SelectItem value="pending">Draft</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="type-filter">Type</Label>
              <Select
                value={filters.submission_type || 'all'}
                onValueChange={(value) => setFilters(prev => ({ ...prev, submission_type: value as any }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All types" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="write">Written</SelectItem>
                  <SelectItem value="upload">Uploaded</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="program-filter">Program</Label>
              <Input
                id="program-filter"
                placeholder="Filter by program..."
                value={filters.program || ''}
                onChange={(e) => setFilters(prev => ({ ...prev, program: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="supervisor-filter">Supervisor</Label>
              <Input
                id="supervisor-filter"
                placeholder="Filter by supervisor..."
                value={filters.supervisor || ''}
                onChange={(e) => setFilters(prev => ({ ...prev, supervisor: e.target.value }))}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Sorting Controls */}
      <Card>
        <CardContent className="py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Label className="text-sm font-medium">Sort by:</Label>
              <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="date">Date</SelectItem>
                  <SelectItem value="student">Student Name</SelectItem>
                  <SelectItem value="category">Category</SelectItem>
                  <SelectItem value="rating">Rating</SelectItem>
                  <SelectItem value="status">Status</SelectItem>
                </SelectContent>
              </Select>

              <Select value={sortOrder} onValueChange={(value: any) => setSortOrder(value)}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="desc">Descending</SelectItem>
                  <SelectItem value="asc">Ascending</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <p className="text-sm text-muted-foreground">
              Showing {filteredFeedback.length} of {feedback.length} feedback items
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Feedback Table */}
      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Student</TableHead>
                <TableHead>Category</TableHead>
                <TableHead>Title</TableHead>
                <TableHead>Rating</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Submitted</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredFeedback.map((item) => {
                const isExpanded = expandedFeedback === item.id

                return (
                  <>
                    <TableRow key={item.id}>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <User className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <span className="font-medium">{item.student_name}</span>
                            <p className="text-xs text-muted-foreground">{item.student_email}</p>
                          </div>
                        </div>
                      </TableCell>

                      <TableCell>
                        <Badge className={getCategoryColor(item.category)}>
                          {getCategoryLabel(item.category)}
                        </Badge>
                      </TableCell>

                      <TableCell>
                        <span className="font-medium">{item.title}</span>
                      </TableCell>

                      <TableCell>
                        {item.rating ? renderStars(item.rating) : 'No rating'}
                      </TableCell>

                      <TableCell>
                        <div className="flex items-center space-x-2">
                          {item.submission_type === 'upload' ? (
                            <Upload className="h-4 w-4 text-blue-500" />
                          ) : (
                            <Edit3 className="h-4 w-4 text-green-500" />
                          )}
                          <span className="capitalize">{item.submission_type}</span>
                        </div>
                      </TableCell>

                      <TableCell>
                        {getStatusBadge(item)}
                      </TableCell>

                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">
                            {item.submitted_at
                              ? new Date(item.submitted_at).toLocaleDateString()
                              : 'Not submitted'
                            }
                          </span>
                        </div>
                      </TableCell>

                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => toggleFeedbackExpansion(item.id!)}
                            className="h-8 w-8 p-0"
                            title="View details"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>

                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeleteFeedback(item)}
                            className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                            title="Delete feedback"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>

                    {/* Expanded Details */}
                    {isExpanded && (
                      <TableRow>
                        <TableCell colSpan={8} className="bg-muted/50">
                          <div className="p-4 space-y-4">
                            <h4 className="font-semibold">Feedback Details</h4>

                            {item.submission_type === 'write' && item.content ? (
                              <div className="space-y-2">
                                <div className="flex items-center justify-between">
                                  <span className="text-sm font-medium">Written Feedback</span>
                                  <span className="text-xs text-muted-foreground">
                                    {item.character_count} characters
                                  </span>
                                </div>
                                <div
                                  className="p-3 bg-background rounded border text-sm max-h-40 overflow-y-auto"
                                  dangerouslySetInnerHTML={{
                                    __html: item.content.length > 500
                                      ? item.content.substring(0, 500) + '...'
                                      : item.content
                                  }}
                                />
                              </div>
                            ) : item.submission_type === 'upload' && item.file_name ? (
                              <div className="space-y-2">
                                <span className="text-sm font-medium">Uploaded File</span>
                                <div className="flex items-center justify-between p-3 bg-background rounded border">
                                  <div className="flex items-center space-x-2">
                                    <Upload className="h-5 w-5 text-blue-500" />
                                    <div>
                                      <p className="font-medium text-sm">{item.file_name}</p>
                                      <p className="text-xs text-muted-foreground">
                                        {item.file_type} • {item.file_size ? formatFileSize(item.file_size) : 'Unknown size'}
                                      </p>
                                    </div>
                                  </div>
                                  <div className="flex items-center space-x-2">
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => handlePreviewFile(item)}
                                    >
                                      <Eye className="h-4 w-4 mr-1" />
                                      Preview
                                    </Button>
                                    {item.file_url && (
                                      <Button variant="outline" size="sm" asChild>
                                        <a href={item.file_url} target="_blank" rel="noopener noreferrer">
                                          <ExternalLink className="h-4 w-4" />
                                        </a>
                                      </Button>
                                    )}
                                  </div>
                                </div>
                              </div>
                            ) : (
                              <div className="p-3 bg-background rounded border text-center text-muted-foreground">
                                <MessageSquare className="h-8 w-8 mx-auto mb-2 opacity-50" />
                                <p>No content available</p>
                              </div>
                            )}

                            {/* Metadata */}
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 pt-2 border-t text-xs text-muted-foreground">
                              <div>
                                <span className="font-medium">Program:</span>
                                <p>{item.program_name || 'N/A'}</p>
                              </div>
                              <div>
                                <span className="font-medium">Supervisor:</span>
                                <p>{item.supervisor_name || 'N/A'}</p>
                              </div>
                              <div>
                                <span className="font-medium">Created:</span>
                                <p>{item.created_at ? new Date(item.created_at).toLocaleString() : 'N/A'}</p>
                              </div>
                              <div>
                                <span className="font-medium">Submitted:</span>
                                <p>{item.submitted_at ? new Date(item.submitted_at).toLocaleString() : 'Not submitted'}</p>
                              </div>
                            </div>
                          </div>
                        </TableCell>
                      </TableRow>
                    )}
                  </>
                )
              })}
            </TableBody>
          </Table>

          {filteredFeedback.length === 0 && (
            <div className="p-8 text-center text-muted-foreground">
              <MessageSquare className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-lg font-medium mb-2">No feedback found</p>
              <p className="text-sm">Try adjusting your filters or check back later</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={deleteConfirmation.isOpen}
        onClose={cancelDeleteFeedback}
        onConfirm={confirmDeleteFeedback}
        title="Delete Feedback"
        description={
          <div className="space-y-2">
            <p>Are you sure you want to delete this feedback?</p>
            <div className="p-3 bg-muted rounded-lg">
              <p className="font-medium">{deleteConfirmation.feedbackTitle}</p>
              <p className="text-sm text-muted-foreground">by {deleteConfirmation.studentName}</p>
            </div>
            <p className="text-sm text-destructive">This action cannot be undone.</p>
          </div>
        }
        confirmText="Delete"
        cancelText="Cancel"
        variant="destructive"
      />

      {/* File Preview Dialog */}
      <AdminFilePreviewDialog
        isOpen={previewDialog.isOpen}
        onClose={closePreview}
        feedback={previewDialog.feedback}
      />
    </div>
  )
}
