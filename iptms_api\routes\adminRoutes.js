const express = require('express');
const { verifyJWT } = require('../middlewares/authMiddleware');
const { getAllFeedback } = require('../controllers/feedbackController');
const router = express.Router();

// Admin reports routes
router.get('/reports/feedback', getAllFeedback);

router.get('/reports/summary', (req, res) => {
    res.status(200).json({
        success: true,
        message: 'Admin reports summary - to be implemented',
        data: null
    });
});

router.get('/reports/logbooks', (req, res) => {
    res.status(200).json({
        success: true,
        message: 'Admin logbooks - to be implemented',
        data: []
    });
});

router.get('/reports/final-reports', (req, res) => {
    res.status(200).json({
        success: true,
        message: 'Admin final reports - to be implemented',
        data: []
    });
});

router.get('/reports/export', (req, res) => {
    res.status(200).json({
        success: true,
        message: 'Export functionality - to be implemented',
        data: null
    });
});

module.exports = router;
