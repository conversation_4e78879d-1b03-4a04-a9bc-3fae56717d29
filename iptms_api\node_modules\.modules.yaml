hoistPattern:
  - '*'
hoistedDependencies:
  '@mapbox/node-pre-gyp@1.0.11':
    '@mapbox/node-pre-gyp': private
  abbrev@1.1.1:
    abbrev: private
  accepts@2.0.0:
    accepts: private
  agent-base@6.0.2:
    agent-base: private
  ansi-regex@5.0.1:
    ansi-regex: private
  anymatch@3.1.3:
    anymatch: private
  aproba@2.0.0:
    aproba: private
  are-we-there-yet@2.0.0:
    are-we-there-yet: private
  aws-ssl-profiles@1.1.2:
    aws-ssl-profiles: private
  balanced-match@1.0.2:
    balanced-match: private
  bignumber.js@9.0.0:
    bignumber.js: private
  binary-extensions@2.3.0:
    binary-extensions: private
  brace-expansion@1.1.11:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  buffer-equal-constant-time@1.0.1:
    buffer-equal-constant-time: private
  bytes@3.1.2:
    bytes: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bound@1.0.4:
    call-bound: private
  chokidar@3.6.0:
    chokidar: private
  chownr@2.0.0:
    chownr: private
  color-support@1.1.3:
    color-support: private
  concat-map@0.0.1:
    concat-map: private
  console-control-strings@1.1.0:
    console-control-strings: private
  content-disposition@1.0.0:
    content-disposition: private
  content-type@1.0.5:
    content-type: private
  cookie-signature@1.2.2:
    cookie-signature: private
  cookie@0.7.1:
    cookie: private
  core-util-is@1.0.3:
    core-util-is: private
  debug@4.4.0(supports-color@5.5.0):
    debug: private
  delegates@1.0.0:
    delegates: private
  denque@2.1.0:
    denque: private
  depd@2.0.0:
    depd: private
  detect-libc@2.0.3:
    detect-libc: private
  dunder-proto@1.0.1:
    dunder-proto: private
  ecdsa-sig-formatter@1.0.11:
    ecdsa-sig-formatter: private
  ee-first@1.1.1:
    ee-first: private
  emoji-regex@8.0.0:
    emoji-regex: private
  encodeurl@2.0.0:
    encodeurl: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  escape-html@1.0.3:
    escape-html: private
  etag@1.8.1:
    etag: private
  fill-range@7.1.1:
    fill-range: private
  finalhandler@2.1.0:
    finalhandler: private
  forwarded@0.2.0:
    forwarded: private
  fresh@2.0.0:
    fresh: private
  fs-minipass@2.1.0:
    fs-minipass: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  gauge@3.0.2:
    gauge: private
  generate-function@2.3.1:
    generate-function: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-proto@1.0.1:
    get-proto: private
  glob-parent@5.1.2:
    glob-parent: private
  glob@7.2.3:
    glob: private
  gopd@1.2.0:
    gopd: private
  has-flag@3.0.0:
    has-flag: private
  has-symbols@1.1.0:
    has-symbols: private
  has-unicode@2.0.1:
    has-unicode: private
  hasown@2.0.2:
    hasown: private
  http-errors@2.0.0:
    http-errors: private
  https-proxy-agent@5.0.1:
    https-proxy-agent: private
  iconv-lite@0.6.3:
    iconv-lite: private
  ignore-by-default@1.0.1:
    ignore-by-default: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  ipaddr.js@1.9.1:
    ipaddr.js: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-extglob@2.1.1:
    is-extglob: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-glob@4.0.3:
    is-glob: private
  is-number@7.0.0:
    is-number: private
  is-promise@4.0.0:
    is-promise: private
  is-property@1.0.2:
    is-property: private
  isarray@1.0.0:
    isarray: private
  jwa@1.4.1:
    jwa: private
  jws@3.2.2:
    jws: private
  lodash.includes@4.3.0:
    lodash.includes: private
  lodash.isboolean@3.0.3:
    lodash.isboolean: private
  lodash.isinteger@4.0.4:
    lodash.isinteger: private
  lodash.isnumber@3.0.3:
    lodash.isnumber: private
  lodash.isplainobject@4.0.6:
    lodash.isplainobject: private
  lodash.isstring@4.0.1:
    lodash.isstring: private
  lodash.once@4.1.1:
    lodash.once: private
  long@5.3.1:
    long: private
  lru-cache@7.18.3:
    lru-cache: private
  lru.min@1.1.2:
    lru.min: private
  make-dir@3.1.0:
    make-dir: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  media-typer@1.1.0:
    media-typer: private
  merge-descriptors@2.0.0:
    merge-descriptors: private
  mime-db@1.54.0:
    mime-db: private
  mime-types@3.0.1:
    mime-types: private
  minimatch@3.1.2:
    minimatch: private
  minipass@5.0.0:
    minipass: private
  minizlib@2.1.2:
    minizlib: private
  mkdirp@1.0.4:
    mkdirp: private
  ms@2.1.3:
    ms: private
  named-placeholders@1.1.3:
    named-placeholders: private
  negotiator@1.0.0:
    negotiator: private
  node-addon-api@5.1.0:
    node-addon-api: private
  node-fetch@2.7.0:
    node-fetch: private
  nopt@5.0.0:
    nopt: private
  normalize-path@3.0.0:
    normalize-path: private
  npmlog@5.0.1:
    npmlog: private
  object-assign@4.1.1:
    object-assign: private
  object-inspect@1.13.4:
    object-inspect: private
  on-finished@2.4.1:
    on-finished: private
  once@1.4.0:
    once: private
  parseurl@1.3.3:
    parseurl: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-to-regexp@8.2.0:
    path-to-regexp: private
  pg-cloudflare@1.2.5:
    pg-cloudflare: private
  pg-connection-string@2.9.0:
    pg-connection-string: private
  pg-int8@1.0.1:
    pg-int8: private
  pg-pool@3.10.0(pg@8.16.0):
    pg-pool: private
  pg-protocol@1.10.0:
    pg-protocol: private
  pg-types@2.2.0:
    pg-types: private
  pgpass@1.0.5:
    pgpass: private
  picomatch@2.3.1:
    picomatch: private
  postgres-array@2.0.0:
    postgres-array: private
  postgres-bytea@1.0.0:
    postgres-bytea: private
  postgres-date@1.0.7:
    postgres-date: private
  postgres-interval@1.2.0:
    postgres-interval: private
  process-nextick-args@2.0.1:
    process-nextick-args: private
  process@0.11.10:
    process: private
  proxy-addr@2.0.7:
    proxy-addr: private
  pstree.remy@1.1.8:
    pstree.remy: private
  qs@6.14.0:
    qs: private
  range-parser@1.2.1:
    range-parser: private
  raw-body@3.0.0:
    raw-body: private
  readable-stream@2.3.7:
    readable-stream: private
  readdirp@3.6.0:
    readdirp: private
  rimraf@3.0.2:
    rimraf: private
  router@2.2.0:
    router: private
  safe-buffer@5.1.2:
    safe-buffer: private
  safer-buffer@2.1.2:
    safer-buffer: private
  semver@7.7.1:
    semver: private
  send@1.2.0:
    send: private
  seq-queue@0.0.5:
    seq-queue: private
  serve-static@2.2.0:
    serve-static: private
  set-blocking@2.0.0:
    set-blocking: private
  setprototypeof@1.2.0:
    setprototypeof: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  signal-exit@3.0.7:
    signal-exit: private
  simple-update-notifier@2.0.0:
    simple-update-notifier: private
  split2@4.2.0:
    split2: private
  sqlstring@2.3.3:
    sqlstring: private
  statuses@2.0.1:
    statuses: private
  string-width@4.2.3:
    string-width: private
  string_decoder@1.1.1:
    string_decoder: private
  strip-ansi@6.0.1:
    strip-ansi: private
  supports-color@5.5.0:
    supports-color: private
  tar@6.2.1:
    tar: private
  to-regex-range@5.0.1:
    to-regex-range: private
  toidentifier@1.0.1:
    toidentifier: private
  touch@3.1.1:
    touch: private
  tr46@0.0.3:
    tr46: private
  type-is@2.0.1:
    type-is: private
  undefsafe@2.0.5:
    undefsafe: private
  unpipe@1.0.0:
    unpipe: private
  util-deprecate@1.0.2:
    util-deprecate: private
  util@0.10.4:
    util: private
  vary@1.1.2:
    vary: private
  webidl-conversions@3.0.1:
    webidl-conversions: private
  whatwg-url@5.0.0:
    whatwg-url: private
  wide-align@1.1.5:
    wide-align: private
  wrappy@1.0.2:
    wrappy: private
  xtend@4.0.2:
    xtend: private
  yallist@4.0.0:
    yallist: private
ignoredBuilds:
  - bcrypt
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.6.2
pendingBuilds: []
prunedAt: Tue, 24 Jun 2025 18:47:08 GMT
publicHoistPattern: []
registries:
  default: https://registry.npmjs.org/
skipped:
  - fsevents@2.3.3
storeDir: D:\.pnpm-store\v10
virtualStoreDir: D:\03EM14\iptms\iptms_api\node_modules\.pnpm
virtualStoreDirMaxLength: 60
