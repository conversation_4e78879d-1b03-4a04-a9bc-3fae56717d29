const db = require('./config/db');

async function createTables() {
    try {
        console.log('Creating feedback table...');
        
        // Create feedback table
        await db.query(`
            CREATE TABLE IF NOT EXISTS feedback (
                id int NOT NULL AUTO_INCREMENT,
                student_id int NOT NULL,
                category enum('general','program','supervision','facilities','recommendations') NOT NULL,
                title varchar(255) NOT NULL,
                submission_type enum('write','upload') NOT NULL,
                content text,
                character_count int DEFAULT NULL,
                file_url varchar(500) DEFAULT NULL,
                file_name varchar(255) DEFAULT NULL,
                file_size bigint DEFAULT NULL,
                file_type varchar(100) DEFAULT NULL,
                rating int DEFAULT NULL,
                is_submitted tinyint(1) DEFAULT '0',
                submitted_at timestamp NULL DEFAULT NULL,
                admin_reviewed tinyint(1) DEFAULT '0',
                admin_response text,
                created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                KEY idx_feedback_student_id (student_id),
                KEY idx_feedback_category (category),
                KEY idx_feedback_submitted (is_submitted),
                KEY idx_feedback_rating (rating),
                KEY idx_feedback_submission_type (submission_type)
            ) ENGINE=MyISAM AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;
        `);
        
        console.log('Creating recommendations table...');
        
        // Create recommendations table
        await db.query(`
            CREATE TABLE IF NOT EXISTS recommendations (
                id int NOT NULL AUTO_INCREMENT,
                student_id int NOT NULL,
                supervisor_id int NOT NULL,
                title varchar(255) NOT NULL,
                content text NOT NULL,
                category enum('academic','professional','personal','technical','general') NOT NULL DEFAULT 'general',
                priority enum('low','medium','high','urgent') NOT NULL DEFAULT 'medium',
                status enum('pending','acknowledged','in_progress','completed','dismissed') NOT NULL DEFAULT 'pending',
                is_read tinyint(1) DEFAULT '0',
                read_at timestamp NULL DEFAULT NULL,
                response text,
                responded_at timestamp NULL DEFAULT NULL,
                created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                KEY idx_recommendations_student_id (student_id),
                KEY idx_recommendations_supervisor_id (supervisor_id),
                KEY idx_recommendations_category (category),
                KEY idx_recommendations_priority (priority),
                KEY idx_recommendations_status (status),
                KEY idx_recommendations_is_read (is_read)
            ) ENGINE=MyISAM AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;
        `);

        console.log('Dropping old logbook tables if they exist...');

        // Drop old tables to recreate with new structure
        await db.query(`DROP TABLE IF EXISTS logbooks`);
        await db.query(`DROP TABLE IF EXISTS weekly_reports`);

        console.log('Creating daily_logs table...');

        // Create daily_logs table for daily entries (Monday-Saturday)
        await db.query(`
            CREATE TABLE IF NOT EXISTS daily_logs (
                id int NOT NULL AUTO_INCREMENT,
                student_id int NOT NULL,
                week_number int NOT NULL CHECK (week_number >= 1 AND week_number <= 10),
                day_of_week enum('monday','tuesday','wednesday','thursday','friday','saturday') NOT NULL,
                date date NOT NULL,
                title varchar(255) NOT NULL,
                activities text NOT NULL,
                tasks_completed text,
                challenges_faced text,
                skills_learned text,
                hours_worked decimal(4,2) DEFAULT NULL,
                supervisor_present tinyint(1) DEFAULT '0',
                supervisor_feedback text,
                file_url varchar(500) DEFAULT NULL,
                file_name varchar(255) DEFAULT NULL,
                file_size bigint DEFAULT NULL,
                file_type varchar(100) DEFAULT NULL,
                status enum('draft','submitted') NOT NULL DEFAULT 'draft',
                is_submitted tinyint(1) DEFAULT '0',
                submitted_at timestamp NULL DEFAULT NULL,
                created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                UNIQUE KEY unique_student_week_day (student_id, week_number, day_of_week),
                KEY idx_daily_logs_student_id (student_id),
                KEY idx_daily_logs_week_number (week_number),
                KEY idx_daily_logs_day_of_week (day_of_week),
                KEY idx_daily_logs_date (date),
                KEY idx_daily_logs_status (status),
                KEY idx_daily_logs_submitted (is_submitted)
            ) ENGINE=MyISAM AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;
        `);

        console.log('Creating weekly_reports table...');

        // Create weekly_reports table for weekly summaries
        await db.query(`
            CREATE TABLE IF NOT EXISTS weekly_reports (
                id int NOT NULL AUTO_INCREMENT,
                student_id int NOT NULL,
                week_number int NOT NULL CHECK (week_number >= 1 AND week_number <= 10),
                week_start_date date NOT NULL,
                week_end_date date NOT NULL,
                summary text NOT NULL,
                objectives_achieved text,
                challenges_encountered text,
                skills_developed text,
                learning_outcomes text,
                areas_for_improvement text,
                next_week_goals text,
                total_hours decimal(5,2) DEFAULT NULL,
                attendance_days int DEFAULT NULL,
                self_assessment text,
                self_rating int DEFAULT NULL CHECK (self_rating >= 1 AND self_rating <= 5),
                supervisor_comments text,
                supervisor_rating int DEFAULT NULL CHECK (supervisor_rating >= 1 AND supervisor_rating <= 5),
                file_url varchar(500) DEFAULT NULL,
                file_name varchar(255) DEFAULT NULL,
                file_size bigint DEFAULT NULL,
                file_type varchar(100) DEFAULT NULL,
                status enum('draft','submitted','reviewed','approved','needs_revision') NOT NULL DEFAULT 'draft',
                is_submitted tinyint(1) DEFAULT '0',
                submitted_at timestamp NULL DEFAULT NULL,
                reviewed_at timestamp NULL DEFAULT NULL,
                supervisor_id int DEFAULT NULL,
                created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                UNIQUE KEY unique_student_week_report (student_id, week_number),
                KEY idx_weekly_reports_student_id (student_id),
                KEY idx_weekly_reports_week_number (week_number),
                KEY idx_weekly_reports_status (status),
                KEY idx_weekly_reports_submitted (is_submitted),
                KEY idx_weekly_reports_supervisor_id (supervisor_id),
                KEY idx_weekly_reports_self_rating (self_rating),
                KEY idx_weekly_reports_supervisor_rating (supervisor_rating)
            ) ENGINE=MyISAM AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;
        `);

        console.log('Tables created successfully!');
        process.exit(0);
        
    } catch (error) {
        console.error('Error creating tables:', error);
        process.exit(1);
    }
}

createTables();
