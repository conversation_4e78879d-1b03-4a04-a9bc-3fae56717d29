const db = require('./config/db');

async function createTables() {
    try {
        console.log('Creating feedback table...');
        
        // Create feedback table
        await db.query(`
            CREATE TABLE IF NOT EXISTS feedback (
                id int NOT NULL AUTO_INCREMENT,
                student_id int NOT NULL,
                category enum('general','program','supervision','facilities','recommendations') NOT NULL,
                title varchar(255) NOT NULL,
                submission_type enum('write','upload') NOT NULL,
                content text,
                character_count int DEFAULT NULL,
                file_url varchar(500) DEFAULT NULL,
                file_name varchar(255) DEFAULT NULL,
                file_size bigint DEFAULT NULL,
                file_type varchar(100) DEFAULT NULL,
                rating int DEFAULT NULL,
                is_submitted tinyint(1) DEFAULT '0',
                submitted_at timestamp NULL DEFAULT NULL,
                admin_reviewed tinyint(1) DEFAULT '0',
                admin_response text,
                created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                KEY idx_feedback_student_id (student_id),
                KEY idx_feedback_category (category),
                KEY idx_feedback_submitted (is_submitted),
                KEY idx_feedback_rating (rating),
                KEY idx_feedback_submission_type (submission_type)
            ) ENGINE=MyISAM AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;
        `);
        
        console.log('Creating recommendations table...');
        
        // Create recommendations table
        await db.query(`
            CREATE TABLE IF NOT EXISTS recommendations (
                id int NOT NULL AUTO_INCREMENT,
                student_id int NOT NULL,
                supervisor_id int NOT NULL,
                title varchar(255) NOT NULL,
                content text NOT NULL,
                category enum('academic','professional','personal','technical','general') NOT NULL DEFAULT 'general',
                priority enum('low','medium','high','urgent') NOT NULL DEFAULT 'medium',
                status enum('pending','acknowledged','in_progress','completed','dismissed') NOT NULL DEFAULT 'pending',
                is_read tinyint(1) DEFAULT '0',
                read_at timestamp NULL DEFAULT NULL,
                response text,
                responded_at timestamp NULL DEFAULT NULL,
                created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                KEY idx_recommendations_student_id (student_id),
                KEY idx_recommendations_supervisor_id (supervisor_id),
                KEY idx_recommendations_category (category),
                KEY idx_recommendations_priority (priority),
                KEY idx_recommendations_status (status),
                KEY idx_recommendations_is_read (is_read)
            ) ENGINE=MyISAM AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;
        `);

        console.log('Creating logbooks table...');

        // Create logbooks table
        await db.query(`
            CREATE TABLE IF NOT EXISTS logbooks (
                id int NOT NULL AUTO_INCREMENT,
                student_id int NOT NULL,
                week_number int NOT NULL,
                title varchar(255) NOT NULL,
                content text,
                activities text,
                challenges text,
                learning_outcomes text,
                supervisor_feedback text,
                file_url varchar(500) DEFAULT NULL,
                file_name varchar(255) DEFAULT NULL,
                file_size bigint DEFAULT NULL,
                file_type varchar(100) DEFAULT NULL,
                status enum('draft','submitted','reviewed','approved','needs_revision') NOT NULL DEFAULT 'draft',
                is_submitted tinyint(1) DEFAULT '0',
                submitted_at timestamp NULL DEFAULT NULL,
                reviewed_at timestamp NULL DEFAULT NULL,
                supervisor_id int DEFAULT NULL,
                hours_logged decimal(5,2) DEFAULT NULL,
                created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                UNIQUE KEY unique_student_week (student_id, week_number),
                KEY idx_logbooks_student_id (student_id),
                KEY idx_logbooks_week_number (week_number),
                KEY idx_logbooks_status (status),
                KEY idx_logbooks_submitted (is_submitted),
                KEY idx_logbooks_supervisor_id (supervisor_id)
            ) ENGINE=MyISAM AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;
        `);

        console.log('Creating weekly_reports table...');

        // Create weekly_reports table for additional weekly progress tracking
        await db.query(`
            CREATE TABLE IF NOT EXISTS weekly_reports (
                id int NOT NULL AUTO_INCREMENT,
                logbook_id int NOT NULL,
                student_id int NOT NULL,
                week_number int NOT NULL,
                objectives_met text,
                skills_developed text,
                areas_for_improvement text,
                next_week_goals text,
                supervisor_comments text,
                self_rating int DEFAULT NULL,
                supervisor_rating int DEFAULT NULL,
                attendance_days int DEFAULT NULL,
                total_hours decimal(5,2) DEFAULT NULL,
                created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (id),
                UNIQUE KEY unique_logbook_week (logbook_id, week_number),
                KEY idx_weekly_reports_logbook_id (logbook_id),
                KEY idx_weekly_reports_student_id (student_id),
                KEY idx_weekly_reports_week_number (week_number),
                KEY idx_weekly_reports_self_rating (self_rating),
                KEY idx_weekly_reports_supervisor_rating (supervisor_rating)
            ) ENGINE=MyISAM AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;
        `);

        console.log('Tables created successfully!');
        process.exit(0);
        
    } catch (error) {
        console.error('Error creating tables:', error);
        process.exit(1);
    }
}

createTables();
