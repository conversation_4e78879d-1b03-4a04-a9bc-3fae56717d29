const DailyLog = require('../models/DailyLog');
const WeeklyReport = require('../models/WeeklyReport');

// Daily Log Controllers

// Get all daily logs (admin only)
const getAllDailyLogs = async (req, res) => {
    try {
        const dailyLogs = await DailyLog.index();
        
        res.status(200).json({
            success: true,
            data: dailyLogs
        });
    } catch (error) {
        console.error('Error fetching all daily logs:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch daily logs',
            error: error.message
        });
    }
};

// Get daily logs for current user (student)
const getMyDailyLogs = async (req, res) => {
    try {
        const { user_id } = req.user;
        const dailyLogs = await DailyLog.getByStudentId(user_id);
        
        res.status(200).json({
            success: true,
            data: dailyLogs
        });
    } catch (error) {
        console.error('Error fetching user daily logs:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch your daily logs',
            error: error.message
        });
    }
};

// Get daily logs for a specific week
const getWeekDailyLogs = async (req, res) => {
    try {
        const { user_id } = req.user;
        const { week_number } = req.params;
        const dailyLogs = await DailyLog.getByWeek(user_id, week_number);
        
        res.status(200).json({
            success: true,
            data: dailyLogs
        });
    } catch (error) {
        console.error('Error fetching week daily logs:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch week daily logs',
            error: error.message
        });
    }
};

// Create new daily log entry
const createDailyLog = async (req, res) => {
    try {
        const { user_id } = req.user;
        const dailyLogData = {
            ...req.body,
            student_id: user_id
        };
        
        const dailyLogId = await DailyLog.create(dailyLogData);
        const newDailyLog = await DailyLog.show(dailyLogId);
        
        res.status(201).json({
            success: true,
            message: 'Daily log created successfully',
            data: newDailyLog
        });
    } catch (error) {
        console.error('Error creating daily log:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to create daily log',
            error: error.message
        });
    }
};

// Update existing daily log entry
const updateDailyLog = async (req, res) => {
    try {
        const { id } = req.params;
        const { user_id } = req.user;
        
        // Check if daily log exists and belongs to user
        const existingDailyLog = await DailyLog.show(id);
        if (!existingDailyLog) {
            return res.status(404).json({
                success: false,
                message: 'Daily log not found'
            });
        }
        
        if (existingDailyLog.student_id !== user_id) {
            return res.status(403).json({
                success: false,
                message: 'You can only update your own daily logs'
            });
        }
        
        const updated = await DailyLog.update(id, req.body);
        
        if (!updated) {
            return res.status(400).json({
                success: false,
                message: 'Failed to update daily log'
            });
        }
        
        const updatedDailyLog = await DailyLog.show(id);
        
        res.status(200).json({
            success: true,
            message: 'Daily log updated successfully',
            data: updatedDailyLog
        });
    } catch (error) {
        console.error('Error updating daily log:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update daily log',
            error: error.message
        });
    }
};

// Delete daily log entry
const deleteDailyLog = async (req, res) => {
    try {
        const { id } = req.params;
        const { user_id } = req.user;
        
        // Check if daily log exists and belongs to user (or user is admin)
        const existingDailyLog = await DailyLog.show(id);
        if (!existingDailyLog) {
            return res.status(404).json({
                success: false,
                message: 'Daily log not found'
            });
        }
        
        // Allow deletion if user owns the daily log or is admin
        if (existingDailyLog.student_id !== user_id && req.user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: 'You can only delete your own daily logs'
            });
        }
        
        const deleted = await DailyLog.delete(id);
        
        if (!deleted) {
            return res.status(400).json({
                success: false,
                message: 'Failed to delete daily log'
            });
        }
        
        res.status(200).json({
            success: true,
            message: 'Daily log deleted successfully'
        });
    } catch (error) {
        console.error('Error deleting daily log:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to delete daily log',
            error: error.message
        });
    }
};

// Weekly Report Controllers

// Get all weekly reports (admin only)
const getAllWeeklyReports = async (req, res) => {
    try {
        const reports = await WeeklyReport.index();
        
        res.status(200).json({
            success: true,
            data: reports
        });
    } catch (error) {
        console.error('Error fetching all weekly reports:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch weekly reports',
            error: error.message
        });
    }
};

// Get weekly reports for current user
const getMyWeeklyReports = async (req, res) => {
    try {
        const { user_id } = req.user;
        const reports = await WeeklyReport.getByStudentId(user_id);
        
        res.status(200).json({
            success: true,
            data: reports
        });
    } catch (error) {
        console.error('Error fetching user weekly reports:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch your weekly reports',
            error: error.message
        });
    }
};

// Get weekly report for specific week
const getWeeklyReport = async (req, res) => {
    try {
        const { user_id } = req.user;
        const { week_number } = req.params;
        const report = await WeeklyReport.getByStudentWeek(user_id, week_number);
        
        res.status(200).json({
            success: true,
            data: report
        });
    } catch (error) {
        console.error('Error fetching weekly report:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch weekly report',
            error: error.message
        });
    }
};

// Create weekly report
const createWeeklyReport = async (req, res) => {
    try {
        const { user_id } = req.user;
        const reportData = {
            ...req.body,
            student_id: user_id
        };
        
        const reportId = await WeeklyReport.create(reportData);
        const newReport = await WeeklyReport.show(reportId);
        
        res.status(201).json({
            success: true,
            message: 'Weekly report created successfully',
            data: newReport
        });
    } catch (error) {
        console.error('Error creating weekly report:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to create weekly report',
            error: error.message
        });
    }
};

// Update weekly report
const updateWeeklyReport = async (req, res) => {
    try {
        const { id } = req.params;
        const { user_id } = req.user;
        
        const existingReport = await WeeklyReport.show(id);
        if (!existingReport) {
            return res.status(404).json({
                success: false,
                message: 'Weekly report not found'
            });
        }
        
        if (existingReport.student_id !== user_id) {
            return res.status(403).json({
                success: false,
                message: 'You can only update your own weekly reports'
            });
        }
        
        const updated = await WeeklyReport.update(id, req.body);
        
        if (!updated) {
            return res.status(400).json({
                success: false,
                message: 'Failed to update weekly report'
            });
        }
        
        const updatedReport = await WeeklyReport.show(id);
        
        res.status(200).json({
            success: true,
            message: 'Weekly report updated successfully',
            data: updatedReport
        });
    } catch (error) {
        console.error('Error updating weekly report:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update weekly report',
            error: error.message
        });
    }
};

module.exports = {
    getAllDailyLogs,
    getMyDailyLogs,
    getWeekDailyLogs,
    createDailyLog,
    updateDailyLog,
    deleteDailyLog,
    getAllWeeklyReports,
    getMyWeeklyReports,
    getWeeklyReport,
    createWeeklyReport,
    updateWeeklyReport
};
