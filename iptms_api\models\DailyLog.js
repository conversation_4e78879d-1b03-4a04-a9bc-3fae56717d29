const db = require('../config/db');

class DailyLog {
    // Get all daily logs with student information (for admin)
    static async index() {
        try {
            const [rows] = await db.query(`
                SELECT
                    dl.*,
                    u.first_name,
                    u.middle_name,
                    u.last_name,
                    u.email,
                    CONCAT(u.first_name, ' ', COALESCE(u.middle_name, ''), ' ', u.last_name) as student_name,
                    u.email as student_email
                FROM daily_logs dl
                INNER JOIN users u ON u.id = dl.student_id
                ORDER BY dl.week_number ASC, dl.date ASC;
            `);
            return rows;
        } catch (error) {
            console.error('Error fetching all daily logs:', error);
            throw error;
        }
    }

    // Get single daily log by ID
    static async show(id) {
        try {
            const [rows] = await db.query(`
                SELECT
                    dl.*,
                    u.first_name,
                    u.middle_name,
                    u.last_name,
                    u.email,
                    CONCAT(u.first_name, ' ', COALESCE(u.middle_name, ''), ' ', u.last_name) as student_name,
                    u.email as student_email
                FROM daily_logs dl
                INNER JOIN users u ON u.id = dl.student_id
                WHERE dl.id = ?;
            `, [id]);

            return rows.length > 0 ? rows[0] : null;
        } catch (error) {
            console.error('Error fetching daily log:', error);
            throw error;
        }
    }

    // Get daily logs for a specific student
    static async getByStudentId(studentId) {
        try {
            const [rows] = await db.query(`
                SELECT * FROM daily_logs
                WHERE student_id = ?
                ORDER BY week_number ASC, date ASC;
            `, [studentId]);
            return rows;
        } catch (error) {
            console.error('Error fetching student daily logs:', error);
            throw error;
        }
    }

    // Get daily logs for a specific week
    static async getByWeek(studentId, weekNumber) {
        try {
            const [rows] = await db.query(`
                SELECT * FROM daily_logs
                WHERE student_id = ? AND week_number = ?
                ORDER BY date ASC;
            `, [studentId, weekNumber]);
            return rows;
        } catch (error) {
            console.error('Error fetching week daily logs:', error);
            throw error;
        }
    }

    // Create new daily log entry
    static async create(data) {
        try {
            const [result] = await db.query(`
                INSERT INTO daily_logs (
                    student_id, week_number, day_of_week, date, title,
                    activities, tasks_completed, challenges_faced, skills_learned,
                    hours_worked, supervisor_present, supervisor_feedback,
                    file_url, file_name, file_size, file_type, status,
                    is_submitted, submitted_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `, [
                data.student_id,
                data.week_number,
                data.day_of_week,
                data.date,
                data.title,
                data.activities,
                data.tasks_completed || null,
                data.challenges_faced || null,
                data.skills_learned || null,
                data.hours_worked || null,
                data.supervisor_present || 0,
                data.supervisor_feedback || null,
                data.file_url || null,
                data.file_name || null,
                data.file_size || null,
                data.file_type || null,
                data.status || 'draft',
                data.is_submitted || 0,
                data.submitted_at || null
            ]);

            return result.insertId;
        } catch (error) {
            console.error('Error creating daily log:', error);
            throw error;
        }
    }

    // Update existing daily log entry
    static async update(id, data) {
        try {
            const [result] = await db.query(`
                UPDATE daily_logs SET
                    week_number = ?,
                    day_of_week = ?,
                    date = ?,
                    title = ?,
                    activities = ?,
                    tasks_completed = ?,
                    challenges_faced = ?,
                    skills_learned = ?,
                    hours_worked = ?,
                    supervisor_present = ?,
                    supervisor_feedback = ?,
                    file_url = ?,
                    file_name = ?,
                    file_size = ?,
                    file_type = ?,
                    status = ?,
                    is_submitted = ?,
                    submitted_at = ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            `, [
                data.week_number,
                data.day_of_week,
                data.date,
                data.title,
                data.activities,
                data.tasks_completed || null,
                data.challenges_faced || null,
                data.skills_learned || null,
                data.hours_worked || null,
                data.supervisor_present || 0,
                data.supervisor_feedback || null,
                data.file_url || null,
                data.file_name || null,
                data.file_size || null,
                data.file_type || null,
                data.status || 'draft',
                data.is_submitted || 0,
                data.submitted_at || null,
                id
            ]);

            return result.affectedRows > 0;
        } catch (error) {
            console.error('Error updating daily log:', error);
            throw error;
        }
    }

    // Delete daily log entry
    static async delete(id) {
        try {
            const [result] = await db.query('DELETE FROM daily_logs WHERE id = ?', [id]);
            return result.affectedRows > 0;
        } catch (error) {
            console.error('Error deleting daily log:', error);
            throw error;
        }
    }

    // Update supervisor feedback
    static async updateSupervisorFeedback(id, feedback) {
        try {
            const [result] = await db.query(`
                UPDATE daily_logs SET
                    supervisor_feedback = ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            `, [feedback, id]);

            return result.affectedRows > 0;
        } catch (error) {
            console.error('Error updating supervisor feedback:', error);
            throw error;
        }
    }

    // Get daily log statistics for admin dashboard
    static async getStatistics() {
        try {
            const [stats] = await db.query(`
                SELECT
                    COUNT(*) as total_daily_logs,
                    COUNT(CASE WHEN is_submitted = 1 THEN 1 END) as submitted_daily_logs,
                    COUNT(DISTINCT student_id) as active_students,
                    AVG(hours_worked) as average_hours_per_day,
                    MAX(week_number) as current_week
                FROM daily_logs
            `);

            return stats[0];
        } catch (error) {
            console.error('Error fetching daily log statistics:', error);
            throw error;
        }
    }

    // Get student progress summary
    static async getStudentProgress(studentId) {
        try {
            const [progress] = await db.query(`
                SELECT
                    COUNT(*) as total_daily_logs,
                    COUNT(CASE WHEN is_submitted = 1 THEN 1 END) as submitted_daily_logs,
                    SUM(hours_worked) as total_hours,
                    AVG(hours_worked) as average_hours_per_day,
                    MAX(week_number) as current_week,
                    MIN(created_at) as started_date,
                    MAX(updated_at) as last_activity
                FROM daily_logs
                WHERE student_id = ?
            `, [studentId]);

            return progress[0];
        } catch (error) {
            console.error('Error fetching student progress:', error);
            throw error;
        }
    }
}

module.exports = DailyLog;
