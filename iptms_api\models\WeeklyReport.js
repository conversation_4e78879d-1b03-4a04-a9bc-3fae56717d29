const db = require('../config/db');

class WeeklyReport {
    // Get all weekly reports with student information
    static async index() {
        try {
            const [rows] = await db.query(`
                SELECT
                    wr.*,
                    u.first_name,
                    u.middle_name,
                    u.last_name,
                    u.email,
                    CONCAT(u.first_name, ' ', COALESCE(u.middle_name, ''), ' ', u.last_name) as student_name,
                    u.email as student_email
                FROM weekly_reports wr
                INNER JOIN users u ON u.id = wr.student_id
                ORDER BY wr.week_number DESC, wr.created_at DESC;
            `);
            return rows;
        } catch (error) {
            console.error('Error fetching all weekly reports:', error);
            throw error;
        }
    }

    // Get single weekly report by ID
    static async show(id) {
        try {
            const [rows] = await db.query(`
                SELECT
                    wr.*,
                    u.first_name,
                    u.middle_name,
                    u.last_name,
                    u.email,
                    CONCAT(u.first_name, ' ', COALESCE(u.middle_name, ''), ' ', u.last_name) as student_name,
                    u.email as student_email
                FROM weekly_reports wr
                INNER JOIN users u ON u.id = wr.student_id
                WHERE wr.id = ?;
            `, [id]);

            return rows.length > 0 ? rows[0] : null;
        } catch (error) {
            console.error('Error fetching weekly report:', error);
            throw error;
        }
    }

    // Get weekly report for a specific student and week
    static async getByStudentWeek(studentId, weekNumber) {
        try {
            const [rows] = await db.query(`
                SELECT * FROM weekly_reports
                WHERE student_id = ? AND week_number = ?;
            `, [studentId, weekNumber]);
            return rows.length > 0 ? rows[0] : null;
        } catch (error) {
            console.error('Error fetching student weekly report:', error);
            throw error;
        }
    }

    // Get weekly reports for a specific student
    static async getByStudentId(studentId) {
        try {
            const [rows] = await db.query(`
                SELECT * FROM weekly_reports
                WHERE student_id = ?
                ORDER BY week_number ASC;
            `, [studentId]);
            return rows;
        } catch (error) {
            console.error('Error fetching student weekly reports:', error);
            throw error;
        }
    }

    // Create new weekly report
    static async create(data) {
        try {
            const [result] = await db.query(`
                INSERT INTO weekly_reports (
                    student_id, week_number, week_start_date, week_end_date,
                    summary, objectives_achieved, challenges_encountered,
                    skills_developed, learning_outcomes, areas_for_improvement,
                    next_week_goals, total_hours, attendance_days,
                    self_assessment, self_rating, supervisor_comments,
                    supervisor_rating, file_url, file_name, file_size,
                    file_type, status, is_submitted, submitted_at, supervisor_id
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `, [
                data.student_id,
                data.week_number,
                data.week_start_date,
                data.week_end_date,
                data.summary,
                data.objectives_achieved || null,
                data.challenges_encountered || null,
                data.skills_developed || null,
                data.learning_outcomes || null,
                data.areas_for_improvement || null,
                data.next_week_goals || null,
                data.total_hours || null,
                data.attendance_days || null,
                data.self_assessment || null,
                data.self_rating || null,
                data.supervisor_comments || null,
                data.supervisor_rating || null,
                data.file_url || null,
                data.file_name || null,
                data.file_size || null,
                data.file_type || null,
                data.status || 'draft',
                data.is_submitted || 0,
                data.submitted_at || null,
                data.supervisor_id || null
            ]);

            return result.insertId;
        } catch (error) {
            console.error('Error creating weekly report:', error);
            throw error;
        }
    }

    // Update existing weekly report
    static async update(id, data) {
        try {
            const [result] = await db.query(`
                UPDATE weekly_reports SET
                    objectives_met = ?,
                    skills_developed = ?,
                    areas_for_improvement = ?,
                    next_week_goals = ?,
                    supervisor_comments = ?,
                    self_rating = ?,
                    supervisor_rating = ?,
                    attendance_days = ?,
                    total_hours = ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            `, [
                data.objectives_met || null,
                data.skills_developed || null,
                data.areas_for_improvement || null,
                data.next_week_goals || null,
                data.supervisor_comments || null,
                data.self_rating || null,
                data.supervisor_rating || null,
                data.attendance_days || null,
                data.total_hours || null,
                id
            ]);
            
            return result.affectedRows > 0;
        } catch (error) {
            console.error('Error updating weekly report:', error);
            throw error;
        }
    }

    // Delete weekly report
    static async delete(id) {
        try {
            const [result] = await db.query('DELETE FROM weekly_reports WHERE id = ?', [id]);
            return result.affectedRows > 0;
        } catch (error) {
            console.error('Error deleting weekly report:', error);
            throw error;
        }
    }

    // Update supervisor rating and comments
    static async updateSupervisorFeedback(id, rating, comments) {
        try {
            const [result] = await db.query(`
                UPDATE weekly_reports SET
                    supervisor_rating = ?,
                    supervisor_comments = ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            `, [rating, comments, id]);
            
            return result.affectedRows > 0;
        } catch (error) {
            console.error('Error updating supervisor feedback:', error);
            throw error;
        }
    }

    // Get weekly report statistics
    static async getStatistics() {
        try {
            const [stats] = await db.query(`
                SELECT 
                    COUNT(*) as total_reports,
                    AVG(self_rating) as average_self_rating,
                    AVG(supervisor_rating) as average_supervisor_rating,
                    AVG(attendance_days) as average_attendance,
                    AVG(total_hours) as average_hours,
                    COUNT(DISTINCT student_id) as active_students,
                    MAX(week_number) as max_week_number
                FROM weekly_reports
            `);
            
            return stats[0];
        } catch (error) {
            console.error('Error fetching weekly report statistics:', error);
            throw error;
        }
    }

    // Get student weekly report summary
    static async getStudentSummary(studentId) {
        try {
            const [summary] = await db.query(`
                SELECT 
                    COUNT(*) as total_reports,
                    AVG(self_rating) as average_self_rating,
                    AVG(supervisor_rating) as average_supervisor_rating,
                    SUM(attendance_days) as total_attendance_days,
                    SUM(total_hours) as total_hours,
                    MAX(week_number) as current_week,
                    MIN(created_at) as first_report_date,
                    MAX(updated_at) as last_report_date
                FROM weekly_reports
                WHERE student_id = ?
            `, [studentId]);
            
            return summary[0];
        } catch (error) {
            console.error('Error fetching student weekly report summary:', error);
            throw error;
        }
    }
}

module.exports = WeeklyReport;
